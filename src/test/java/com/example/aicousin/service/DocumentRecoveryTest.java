package com.example.aicousin.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 文档恢复功能测试
 */
@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
    "milvus.host=************",
    "milvus.port=2323",
    "milvus.database=default",
    "milvus.document-collection.name=ai_cousin_documents",
    "milvus.document-collection.dimension=1536",
    "milvus.document-collection.metric-type=COSINE",
    "milvus.schema-collection.name=ai_cousin_schema",
    "milvus.schema-collection.dimension=1536",
    "milvus.schema-collection.metric-type=COSINE"
})
public class DocumentRecoveryTest {

    @Autowired
    private DocumentService documentService;

    @Autowired
    private MilvusVectorService milvusVectorService;

    @Test
    public void testDocumentRecovery() {
        log.info("🧪 开始测试文档恢复功能...");

        try {
            // 1. 获取当前文档数量
            int initialCount = documentService.getDocumentCount();
            log.info("📊 初始文档数量: {}", initialCount);

            // 2. 获取Milvus中的文档元数据
            List<Map<String, Object>> milvusDocuments = milvusVectorService.getAllDocumentMetadata();
            log.info("📊 Milvus中的文档数量: {}", milvusDocuments.size());

            // 3. 如果Milvus中有文档，测试恢复功能
            if (!milvusDocuments.isEmpty()) {
                // 清空内存中的文档
                documentService.clearDocuments();
                int afterClearCount = documentService.getDocumentCount();
                log.info("📊 清空后文档数量: {}", afterClearCount);

                // 恢复文档
                documentService.recoverDocumentsFromMilvus();
                int afterRecoveryCount = documentService.getDocumentCount();
                log.info("📊 恢复后文档数量: {}", afterRecoveryCount);

                // 验证恢复结果
                if (afterRecoveryCount > 0) {
                    log.info("✅ 文档恢复成功！恢复了 {} 个文档", afterRecoveryCount);
                    
                    // 显示恢复的文档信息
                    List<DocumentService.DocumentInfo> documents = documentService.getAllDocuments();
                    for (DocumentService.DocumentInfo doc : documents) {
                        log.info("📄 恢复的文档: {} (ID: {}, 分块数: {}, 状态: {})", 
                            doc.getName(), doc.getId(), doc.getChunks(), doc.getStatus());
                    }
                } else {
                    log.warn("⚠️ 文档恢复失败，没有恢复任何文档");
                }
            } else {
                log.info("ℹ️ Milvus中没有文档，无法测试恢复功能");
            }

            // 4. 获取Milvus统计信息
            Map<String, Object> stats = milvusVectorService.getCollectionStats();
            log.info("📊 Milvus统计信息: {}", stats);

        } catch (Exception e) {
            log.error("❌ 测试过程中发生错误", e);
        }

        log.info("🧪 文档恢复功能测试完成");
    }

    @Test
    public void testMilvusConnection() {
        log.info("🧪 测试Milvus连接...");

        try {
            // 获取集合统计信息来验证连接
            Map<String, Object> stats = milvusVectorService.getCollectionStats();
            log.info("✅ Milvus连接正常，统计信息: {}", stats);
        } catch (Exception e) {
            log.error("❌ Milvus连接失败", e);
        }
    }
}
