package com.example.aicousin;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "spring.ai.openai.api-key=test-key",
    "milvus.host=localhost",
    "milvus.port=19530",
    "logging.level.com.example.aicousin=INFO"
})
class ApplicationStartupTest {

    @Test
    void contextLoads() {
        // This test ensures that the Spring context loads successfully
        // It will fail if there are any configuration or dependency issues
    }
}
