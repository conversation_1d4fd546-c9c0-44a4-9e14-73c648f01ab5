spring:
  application:
    name: ai-cousin-rag
  ai:
    openai:
      api-key: ${DEEPSEEK_API_KEY}
      base-url: ${DEEPSEEK_BASE_URL:https://api.deepseek.com}
      chat:
        options:
          model: ${DEEPSEEK_CHAT_MODEL:deepseek-chat}
          temperature: 0.7
          max-tokens: 1000
      embedding:
        options:
          model: ${DEEPSEEK_EMBEDDING_MODEL:text-embedding-ada-002}
          # 注意：如果DeepSeek有自己的embedding模型，请替换为相应的模型名称

# Milvus Configuration for Docker
milvus:
  host: ${MILVUS_HOST:************}
  port: ${MILVUS_PORT:19530}
  database: ${MILVUS_DATABASE:default}
  collection:
    name: ${MILVUS_COLLECTION:document_vectors}
    dimension: 1536
    metric-type: COSINE
    index-type: IVF_FLAT
    nlist: 1024

# RAG Configuration
rag:
  chunk:
    size: 1000
    overlap: 200
  retrieval:
    top-k: 5
    similarity-threshold: 0.7

# Server Configuration
server:
  port: 8080

# Logging for Docker
logging:
  level:
    org.springframework.ai: INFO
    io.milvus: INFO
    com.example.aicousin: INFO
  file:
    name: /app/logs/ai-cousin.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
