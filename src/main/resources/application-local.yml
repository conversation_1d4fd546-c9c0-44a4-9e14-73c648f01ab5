spring:
  application:
    name: ai-cousin-rag-local
  ai:
    openai:
      api-key: ${DEEPSEEK_API_KEY:sk-your-deepseek-api-key-here}
      base-url: ${DEEPSEEK_BASE_URL:https://api.deepseek.com}
      chat:
        options:
          model: ${DEEPSEEK_CHAT_MODEL:deepseek-chat}
          temperature: 0.7
          max-tokens: 1000
      embedding:
        options:
          model: ${DEEPSEEK_EMBEDDING_MODEL:text-embedding-ada-002}

# Milvus Configuration for Local Development (Remote Service)
milvus:
  host: ${MILVUS_HOST:your-remote-milvus-host}
  port: ${MILVUS_PORT:19530}
  database: ${MILVUS_DATABASE:default}
  # 如果需要认证，请取消注释并设置
  # username: ${MILVUS_USERNAME:}
  # password: ${MILVUS_PASSWORD:}
  # token: ${MILVUS_TOKEN:}
  collection:
    name: ${MILVUS_COLLECTION:document_vectors_local}
    dimension: 1536
    metric-type: COSINE
    index-type: IVF_FLAT
    nlist: 1024

# RAG Configuration
rag:
  chunk:
    size: 1000
    overlap: 200
  retrieval:
    top-k: 5
    similarity-threshold: 0.7

# Server Configuration
server:
  port: 8081  # 修改为8081避免端口冲突

# Logging for Local Development
logging:
  level:
    org.springframework.ai: DEBUG
    io.milvus: INFO
    com.example.aicousin: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Development specific settings
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
