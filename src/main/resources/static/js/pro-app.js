/**
 * AI Cousin Pro - Modern Apple Style Application
 * Complete frontend application with backend API integration
 */

class AiCousinProApp {
    constructor() {
        this.apiBase = '/api';
        this.currentSection = 'dashboard';
        this.documents = new Map();
        this.chatHistory = [];
        this.chatSessions = [];
        this.currentChatSession = null;
        this.isProcessing = false;
        this.theme = localStorage.getItem('theme') || 'light';
        this.notificationCount = 0;
        this.notifications = [];
        this.notificationPanelOpen = false;

        this.init();
    }

    async init() {
        console.log('🚀 AI Cousin Pro 初始化...');
        
        this.setupEventListeners();
        this.setupDragAndDrop();
        this.initializeTheme();
        this.initializeNavigation();
        
        // 延迟加载数据
        setTimeout(() => {
            this.loadDashboardData();
            this.loadChatSessions();
        }, 500);
        
        console.log('✅ AI Cousin Pro 初始化完成');
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.currentTarget.dataset.section;
                this.switchToSection(section);
            });
        });

        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Notification button
        const notificationBtn = document.getElementById('notificationBtn');
        if (notificationBtn) {
            notificationBtn.addEventListener('click', () => this.toggleNotificationPanel());
        }

        // Close notification panel when clicking outside
        document.addEventListener('click', (e) => {
            const notificationPanel = document.getElementById('notificationPanel');
            const notificationBtn = document.getElementById('notificationBtn');
            const chatDropdown = document.getElementById('chatDropdownMenu');

            if (this.notificationPanelOpen &&
                notificationPanel &&
                !notificationPanel.contains(e.target) &&
                !notificationBtn.contains(e.target)) {
                this.closeNotificationPanel();
            }

            // Close chat dropdown when clicking outside
            if (chatDropdown && chatDropdown.classList.contains('active') &&
                !chatDropdown.contains(e.target) &&
                !e.target.closest('.dropdown-toggle')) {
                this.closeChatMenu();
            }
        });

        // File upload
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                if (e.target.files[0]) {
                    this.handleFileUpload(e.target.files[0]);
                }
            });
        }

        // Chat functionality
        const sendButton = document.getElementById('sendButton');
        const chatInput = document.getElementById('chatInput');
        
        if (sendButton) {
            sendButton.addEventListener('click', () => this.sendMessage());
        }

        if (chatInput) {
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });

            // Auto-resize textarea
            chatInput.addEventListener('input', () => {
                chatInput.style.height = 'auto';
                chatInput.style.height = Math.min(chatInput.scrollHeight, 120) + 'px';
            });
        }

        // Similarity slider
        const similaritySlider = document.getElementById('similarityThreshold');
        const similarityValue = document.getElementById('similarityValue');
        
        if (similaritySlider && similarityValue) {
            similaritySlider.addEventListener('input', (e) => {
                const value = parseFloat(e.target.value);
                similarityValue.textContent = value.toFixed(1);
            });
        }

        // Upload area click
        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            uploadArea.addEventListener('click', () => {
                document.getElementById('fileInput').click();
            });
        }

        // Document search
        const documentSearch = document.getElementById('documentSearch');
        if (documentSearch) {
            documentSearch.addEventListener('input', (e) => {
                this.filterDocuments(e.target.value);
            });
        }
    }

    setupDragAndDrop() {
        const uploadArea = document.getElementById('uploadArea');
        if (!uploadArea) return;

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.add('dragover');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.remove('dragover');
            }, false);
        });

        uploadArea.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileUpload(files[0]);
            }
        }, false);
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    initializeTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        const themeIcon = document.querySelector('#themeToggle i');
        if (themeIcon) {
            themeIcon.className = this.theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }
    }

    toggleTheme() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        localStorage.setItem('theme', this.theme);
        this.initializeTheme();
        this.showToast('info', '主题切换', `已切换到${this.theme === 'dark' ? '深色' : '浅色'}模式`);
    }

    initializeNavigation() {
        // Set initial active section
        this.switchToSection('dashboard');
    }

    switchToSection(sectionId) {
        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');

        // Update content sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(sectionId).classList.add('active');

        this.currentSection = sectionId;

        // Load section-specific data
        this.loadSectionData(sectionId);
    }

    async loadSectionData(sectionId) {
        switch (sectionId) {
            case 'dashboard':
                await this.loadDashboardData();
                break;
            case 'documents':
                await this.loadDocuments();
                break;
            case 'chat':
                // Chat data is loaded on demand
                break;
            case 'database':
                // Database data is loaded on demand
                break;
            case 'settings':
                await this.loadSettings();
                break;
        }
    }

    async loadDashboardData() {
        try {
            // Load system status
            await this.loadSystemStatus();
            
            // Load statistics
            await this.loadStatistics();
            
            // Load cache stats
            await this.loadCacheStats();
            
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            this.showToast('error', '加载失败', '仪表板数据加载失败');
        }
    }

    async loadSystemStatus() {
        try {
            // Check RAG health
            const healthResponse = await fetch(`${this.apiBase}/rag/health`);
            this.updateStatusIndicator('systemStatus', healthResponse.ok, 
                healthResponse.ok ? '系统运行正常' : '系统异常');

            // Check embedding status
            const embeddingResponse = await fetch(`${this.apiBase}/embedding/status`);
            if (embeddingResponse.ok) {
                const embeddingData = await embeddingResponse.json();
                this.updateStatusIndicator('embeddingStatus', true, embeddingData.embeddingStatus);
            } else {
                this.updateStatusIndicator('embeddingStatus', false, '向量化服务异常');
            }

            // Milvus status (assume online if system is healthy)
            this.updateStatusIndicator('milvusStatus', healthResponse.ok, 
                healthResponse.ok ? 'Milvus连接正常' : 'Milvus连接异常');

            // Cache status
            this.updateStatusIndicator('cacheStatus', true, '缓存系统正常');

        } catch (error) {
            console.error('Failed to load system status:', error);
            this.updateStatusIndicator('systemStatus', false, '无法连接到服务器');
            this.updateStatusIndicator('embeddingStatus', false, '服务不可用');
            this.updateStatusIndicator('milvusStatus', false, '服务不可用');
            this.updateStatusIndicator('cacheStatus', false, '服务不可用');
        }
    }

    updateStatusIndicator(elementId, isOnline, statusText) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const indicator = element.querySelector('.status-indicator');
        const text = element.querySelector('.status-text');

        if (indicator) {
            indicator.className = 'status-indicator';
            if (isOnline) {
                indicator.classList.add('online');
            } else {
                indicator.classList.add('offline');
            }
        }

        if (text) {
            text.textContent = statusText;
        }
    }

    async loadStatistics() {
        try {
            // Load documents count
            const documentsResponse = await fetch(`${this.apiBase}/rag/documents`);
            if (documentsResponse.ok) {
                const documentsData = await documentsResponse.json();
                console.log('📊 文档统计:', documentsData);
                this.updateStatCard('totalDocuments', documentsData.total || documentsData.documents?.length || 0);
            }

            // Mock other statistics (these would come from actual APIs)
            this.updateStatCard('totalQueries', this.chatHistory.length);
            this.updateStatCard('connectedDatabases', 0); // Would be from database API

        } catch (error) {
            console.error('Failed to load statistics:', error);
        }
    }

    // 添加诊断功能
    async runDiagnostics() {
        console.log('🔧 开始系统诊断...');

        try {
            // 1. 检查文档列表
            console.log('1️⃣ 检查文档列表...');
            const documentsResponse = await fetch(`${this.apiBase}/rag/documents`);
            if (documentsResponse.ok) {
                const documentsData = await documentsResponse.json();
                console.log('📄 文档数据:', documentsData);

                if (documentsData.documents && documentsData.documents.length > 0) {
                    console.log(`✅ 发现 ${documentsData.documents.length} 个文档`);
                    documentsData.documents.forEach((doc, index) => {
                        console.log(`  ${index + 1}. ${doc.name} (${doc.chunks || 0} 块, 状态: ${doc.status})`);
                    });
                } else {
                    console.log('⚠️ 没有找到任何文档');
                }
            } else {
                console.error('❌ 无法获取文档列表');
            }

            // 2. 检查向量化服务状态
            console.log('2️⃣ 检查向量化服务状态...');
            const embeddingResponse = await fetch(`${this.apiBase}/embedding/status`);
            if (embeddingResponse.ok) {
                const embeddingData = await embeddingResponse.json();
                console.log('🔧 向量化服务状态:', embeddingData);
            }

            // 3. 测试向量化生成
            console.log('3️⃣ 测试向量化生成...');
            const testResponse = await fetch(`${this.apiBase}/embedding/test`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ text: '测试文本' })
            });
            if (testResponse.ok) {
                const testData = await testResponse.json();
                console.log('🧪 向量化测试结果:', testData);
            }

            // 4. 测试查询（如果有文档）
            if (this.documents.size > 0) {
                console.log('4️⃣ 测试查询功能...');
                const queryResponse = await fetch(`${this.apiBase}/rag/query`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        query: '测试查询',
                        sourceType: 'all',
                        topK: 5,
                        similarityThreshold: 0.0
                    })
                });
                if (queryResponse.ok) {
                    const queryData = await queryResponse.json();
                    console.log('🔍 查询测试结果:', queryData);
                }
            }

            console.log('✅ 诊断完成');
            this.showToast('success', '诊断完成', '请查看浏览器控制台了解详细信息');

        } catch (error) {
            console.error('❌ 诊断过程中出错:', error);
            this.showToast('error', '诊断失败', error.message);
        }
    }

    async loadCacheStats() {
        try {
            const response = await fetch(`${this.apiBase}/embedding/cache/stats`);
            if (response.ok) {
                const data = await response.json();
                if (data.status === 'success' && data.cacheStats) {
                    this.updateStatCard('cacheHitRate', data.cacheStats.hitRate);
                }
            }
        } catch (error) {
            console.error('Failed to load cache stats:', error);
        }
    }

    updateStatCard(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            if (typeof value === 'number') {
                this.animateNumber(element, value);
            } else {
                element.textContent = value;
            }
        }
    }

    animateNumber(element, targetNumber) {
        const startNumber = parseInt(element.textContent) || 0;
        const duration = 1000;
        const startTime = Date.now();
        
        const updateNumber = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const currentNumber = Math.floor(startNumber + (targetNumber - startNumber) * progress);
            
            element.textContent = currentNumber;
            
            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        };
        
        updateNumber();
    }

    async handleFileUpload(file) {
        if (this.isProcessing) {
            this.showToast('warning', '处理中', '请等待当前文件处理完成');
            return;
        }

        // Validate file
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = ['.pdf', '.docx', '.txt', '.md'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

        if (file.size > maxSize) {
            this.showToast('error', '文件过大', '文件大小不能超过 10MB');
            return;
        }

        if (!allowedTypes.includes(fileExtension)) {
            this.showToast('error', '文件格式不支持', '请上传 PDF、DOCX、TXT 或 MD 格式的文件');
            return;
        }

        this.isProcessing = true;
        this.showLoading('正在上传和处理文档...');

        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch(`${this.apiBase}/rag/documents`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.status === 'SUCCESS') {
                this.showToast('success', '上传成功', `文档 "${result.documentName}" 已成功处理`);
                await this.loadDocuments();
                await this.loadStatistics();
                
                // Switch to documents tab if not already there
                if (this.currentSection !== 'documents') {
                    this.switchToSection('documents');
                }
            } else {
                this.showToast('error', '上传失败', result.message || '文档处理失败');
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.showToast('error', '上传失败', '网络错误，请重试');
        } finally {
            this.isProcessing = false;
            this.hideLoading();
        }
    }

    async loadDocuments() {
        try {
            console.log('🔄 Loading documents...');
            const response = await fetch(`${this.apiBase}/rag/documents`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('📄 Documents response:', data);

            if (data.documents) {
                this.documents.clear();
                data.documents.forEach(doc => {
                    // 适配后端返回的数据格式
                    const documentData = {
                        id: doc.id,
                        fileName: doc.name || doc.originalName,
                        fileSize: this.parseFileSize(doc.size), // 后端返回的是格式化字符串
                        uploadTime: doc.uploadTime,
                        chunksCount: doc.chunks || 0,
                        status: doc.status,
                        contentType: doc.contentType
                    };
                    this.documents.set(doc.id, documentData);
                });

                console.log(`✅ Loaded ${this.documents.size} documents`);
                this.renderDocuments();
            } else {
                console.warn('⚠️ No documents field in response:', data);
                this.renderDocuments(); // 渲染空状态
            }
        } catch (error) {
            console.error('❌ Failed to load documents:', error);
            this.showToast('error', '加载失败', `无法加载文档列表: ${error.message}`);
            this.renderDocuments(); // 渲染空状态
        }
    }

    // 解析文件大小字符串为数字（用于排序等）
    parseFileSize(sizeStr) {
        if (typeof sizeStr === 'number') return sizeStr;
        if (typeof sizeStr !== 'string') return 0;

        const match = sizeStr.match(/^([\d.]+)\s*([KMGT]?B)$/i);
        if (!match) return 0;

        const value = parseFloat(match[1]);
        const unit = match[2].toUpperCase();

        const multipliers = {
            'B': 1,
            'KB': 1024,
            'MB': 1024 * 1024,
            'GB': 1024 * 1024 * 1024,
            'TB': 1024 * 1024 * 1024 * 1024
        };

        return value * (multipliers[unit] || 1);
    }

    renderDocuments() {
        const documentsList = document.getElementById('documentsList');
        if (!documentsList) return;

        if (this.documents.size === 0) {
            documentsList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <h3>暂无文档</h3>
                    <p>上传您的第一个文档开始使用AI助手</p>
                </div>
            `;
            return;
        }

        const documentsHtml = Array.from(this.documents.values()).map(doc => `
            <div class="document-card" data-id="${doc.id}">
                <div class="document-header">
                    <div class="document-icon">
                        <i class="fas fa-${this.getFileIcon(doc.fileName)}"></i>
                    </div>
                    <div class="document-info">
                        <h4>${doc.fileName}</h4>
                        <div class="document-meta">
                            ${doc.fileSize || '未知大小'} • ${doc.uploadTime || '未知时间'}
                        </div>
                    </div>
                </div>
                <div class="document-body">
                    <div class="document-stats">
                        <div class="stat-badge">
                            <i class="fas fa-puzzle-piece"></i>
                            <span>${doc.chunksCount || 0} 块</span>
                        </div>
                        <div class="stat-badge">
                            <i class="fas fa-check-circle"></i>
                            <span>${doc.status || '未知'}</span>
                        </div>
                    </div>
                    <div class="document-actions">
                        <button class="btn btn-outline btn-small" onclick="app.viewDocument('${doc.id}')">
                            <i class="fas fa-eye"></i>
                            查看
                        </button>
                        <button class="btn btn-danger btn-small" onclick="app.deleteDocument('${doc.id}')">
                            <i class="fas fa-trash"></i>
                            删除
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        documentsList.innerHTML = documentsHtml;
    }

    getFileIcon(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        const iconMap = {
            'pdf': 'file-pdf',
            'docx': 'file-word',
            'doc': 'file-word',
            'txt': 'file-alt',
            'md': 'file-code'
        };
        return iconMap[extension] || 'file';
    }

    formatFileSize(bytes) {
        // 如果已经是格式化的字符串，直接返回
        if (typeof bytes === 'string') return bytes;

        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDate(dateStr) {
        // 如果已经是格式化的字符串，直接返回
        if (typeof dateStr === 'string' && dateStr.includes('-')) {
            return dateStr;
        }

        // 如果是时间戳，转换为日期
        const date = typeof dateStr === 'number' ? new Date(dateStr) : new Date(dateStr);

        if (isNaN(date.getTime())) {
            return '未知时间';
        }

        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    filterDocuments(searchTerm) {
        const documentCards = document.querySelectorAll('.document-card');
        const term = searchTerm.toLowerCase();

        documentCards.forEach(card => {
            const fileName = card.querySelector('.document-info h4').textContent.toLowerCase();
            if (fileName.includes(term)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }

    async deleteDocument(documentId) {
        if (!confirm('确定要删除这个文档吗？此操作不可撤销。')) {
            return;
        }

        this.showLoading('正在删除文档...');

        try {
            const response = await fetch(`${this.apiBase}/rag/documents/${documentId}`, {
                method: 'DELETE'
            });

            const result = await response.json();
            
            if (result.status === 'SUCCESS') {
                this.showToast('success', '删除成功', '文档已成功删除');
                await this.loadDocuments();
                await this.loadStatistics();
            } else {
                this.showToast('error', '删除失败', result.message || '删除文档时发生错误');
            }
        } catch (error) {
            console.error('Delete error:', error);
            this.showToast('error', '删除失败', '网络错误，请重试');
        } finally {
            this.hideLoading();
        }
    }

    viewDocument(documentId) {
        // This would open a document viewer modal
        this.showToast('info', '查看文档', '文档查看功能开发中...');
    }

    async sendMessage() {
        const chatInput = document.getElementById('chatInput');
        const message = chatInput.value.trim();

        if (!message || this.isProcessing) return;

        this.isProcessing = true;
        chatInput.value = '';
        chatInput.style.height = 'auto';

        // Add user message to chat
        this.addMessageToChat('user', message);

        // Get query parameters
        const sourceType = document.getElementById('sourceType').value;
        const topK = parseInt(document.getElementById('topK').value);
        const similarityThreshold = parseFloat(document.getElementById('similarityThreshold').value);

        const queryRequest = {
            query: message,
            sourceType: sourceType,
            topK: topK,
            similarityThreshold: similarityThreshold
        };

        console.log('🔍 发送查询请求:', queryRequest);

        try {
            const response = await fetch(`${this.apiBase}/rag/query`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(queryRequest)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('📄 查询响应:', result);

            if (result.answer) {
                // 显示检索统计信息
                if (result.retrievedChunks && result.retrievedChunks.length > 0) {
                    console.log(`✅ 检索到 ${result.retrievedChunks.length} 个相关片段:`);
                    result.retrievedChunks.forEach((chunk, index) => {
                        console.log(`  ${index + 1}. [${chunk.sourceType}] ${chunk.documentName || chunk.dbName} - 相似度: ${(chunk.similarity * 100).toFixed(1)}%`);
                    });
                } else {
                    console.log('⚠️ 没有检索到相关片段，使用直接聊天模式');
                }

                // 添加消息到聊天界面
                this.addMessageToChat('assistant', result.answer, result.retrievedChunks);

                // 添加到聊天历史
                this.chatHistory.push({
                    role: 'user',
                    content: message,
                    timestamp: new Date()
                });
                this.chatHistory.push({
                    role: 'assistant',
                    content: result.answer,
                    sources: result.retrievedChunks,
                    timestamp: new Date()
                });

                // 创建或更新当前会话
                if (!this.currentChatSession) {
                    this.currentChatSession = {
                        id: Date.now(),
                        title: message.substring(0, 30) + (message.length > 30 ? '...' : ''),
                        messages: [],
                        createdAt: new Date(),
                        updatedAt: new Date()
                    };
                }

                // 保存会话
                this.saveChatSession();

            } else {
                console.error('❌ 响应中没有answer字段:', result);
                this.addMessageToChat('assistant', '抱歉，我无法理解您的问题。请尝试重新表述。');
            }
        } catch (error) {
            console.error('❌ 查询错误:', error);
            this.addMessageToChat('assistant', `抱歉，服务暂时不可用: ${error.message}`);
        } finally {
            this.isProcessing = false;
        }
    }

    addMessageToChat(role, content, sources = null, addToHistory = true) {
        const chatMessages = document.getElementById('chatMessages');
        const welcomeMessage = chatMessages.querySelector('.welcome-message');

        // Remove welcome message on first interaction
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}-message`;
        
        const timestamp = new Date().toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });

        let sourcesHtml = '';
        if (sources && sources.length > 0) {
            sourcesHtml = `
                <div class="message-sources">
                    <div class="sources-header">
                        <i class="fas fa-link"></i>
                        <span>参考来源</span>
                    </div>
                    <div class="sources-list">
                        ${sources.map((source, index) => `
                            <div class="source-item">
                                <span class="source-index">${index + 1}</span>
                                <div class="source-info">
                                    <div class="source-name">${source.documentName || '未知文档'}</div>
                                    <div class="source-similarity">相似度: ${(source.similarity * 100).toFixed(1)}%</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-${role === 'user' ? 'user' : 'robot'}"></i>
            </div>
            <div class="message-content">
                <div class="message-header">
                    <span class="message-sender">${role === 'user' ? '您' : 'AI助手'}</span>
                    <span class="message-time">${timestamp}</span>
                </div>
                <div class="message-text">${this.formatMessage(content)}</div>
                ${sourcesHtml}
            </div>
        `;

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    formatMessage(content) {
        // Simple markdown-like formatting
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>');
    }

    newChat() {
        // 保存当前会话（如果有消息）
        if (this.chatHistory.length > 0) {
            this.saveChatSession();
        }

        // 创建新会话
        this.currentChatSession = {
            id: Date.now(),
            title: '新对话',
            messages: [],
            createdAt: new Date(),
            updatedAt: new Date()
        };

        const chatMessages = document.getElementById('chatMessages');
        chatMessages.innerHTML = `
            <div class="welcome-message">
                <div class="welcome-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <h3>新的对话</h3>
                <p>您好！我是您的智能文档助手，有什么可以帮助您的？</p>
            </div>
        `;

        this.chatHistory = [];
        this.renderChatHistory();
    }

    saveChatSession() {
        if (!this.currentChatSession || this.chatHistory.length === 0) return;

        // 更新会话信息
        this.currentChatSession.messages = [...this.chatHistory];
        this.currentChatSession.updatedAt = new Date();

        // 生成会话标题（使用第一个用户消息）
        const firstUserMessage = this.chatHistory.find(msg => msg.role === 'user');
        if (firstUserMessage) {
            this.currentChatSession.title = firstUserMessage.content.substring(0, 30) +
                (firstUserMessage.content.length > 30 ? '...' : '');
        }

        // 添加到会话列表
        const existingIndex = this.chatSessions.findIndex(s => s.id === this.currentChatSession.id);
        if (existingIndex >= 0) {
            this.chatSessions[existingIndex] = this.currentChatSession;
        } else {
            this.chatSessions.unshift(this.currentChatSession);
        }

        // 限制会话数量
        if (this.chatSessions.length > 20) {
            this.chatSessions = this.chatSessions.slice(0, 20);
        }

        // 保存到本地存储
        localStorage.setItem('chatSessions', JSON.stringify(this.chatSessions));
    }

    loadChatSessions() {
        try {
            const saved = localStorage.getItem('chatSessions');
            if (saved) {
                this.chatSessions = JSON.parse(saved);
                // 转换日期字符串为Date对象
                this.chatSessions.forEach(session => {
                    session.createdAt = new Date(session.createdAt);
                    session.updatedAt = new Date(session.updatedAt);
                    session.messages.forEach(msg => {
                        msg.timestamp = new Date(msg.timestamp);
                    });
                });
            }
        } catch (error) {
            console.error('Failed to load chat sessions:', error);
            this.chatSessions = [];
        }
        this.renderChatHistory();
    }

    loadChatSession(sessionId) {
        const session = this.chatSessions.find(s => s.id === sessionId);
        if (!session) return;

        // 保存当前会话
        if (this.currentChatSession && this.chatHistory.length > 0) {
            this.saveChatSession();
        }

        // 加载选中的会话
        this.currentChatSession = session;
        this.chatHistory = [...session.messages];

        // 渲染消息
        this.renderChatMessages();
        this.renderChatHistory();
    }

    renderChatMessages() {
        const chatMessages = document.getElementById('chatMessages');

        if (this.chatHistory.length === 0) {
            chatMessages.innerHTML = `
                <div class="welcome-message">
                    <div class="welcome-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3>开始新对话</h3>
                    <p>您好！我是您的智能文档助手，有什么可以帮助您的？</p>
                </div>
            `;
            return;
        }

        // 清空欢迎消息
        chatMessages.innerHTML = '';

        // 渲染所有消息
        this.chatHistory.forEach(msg => {
            this.addMessageToChat(msg.role, msg.content, msg.sources, false);
        });
    }

    renderChatHistory() {
        const chatHistoryContainer = document.getElementById('chatHistory');
        if (!chatHistoryContainer) return;

        if (this.chatSessions.length === 0) {
            chatHistoryContainer.innerHTML = `
                <div class="chat-history-empty">
                    <div class="empty-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h4>暂无对话历史</h4>
                    <p>开始新对话后会显示在这里</p>
                </div>
            `;
            return;
        }

        const historyHtml = this.chatSessions.map(session => {
            const isActive = this.currentChatSession && this.currentChatSession.id === session.id;
            const preview = session.messages.length > 0 ?
                session.messages[session.messages.length - 1].content.substring(0, 50) + '...' :
                '新对话';

            return `
                <div class="chat-history-item ${isActive ? 'active' : ''}"
                     onclick="app.loadChatSession(${session.id})">
                    <div class="chat-history-title">${session.title}</div>
                    <div class="chat-history-preview">${preview}</div>
                    <div class="chat-history-time">${this.formatChatTime(session.updatedAt)}</div>
                    <button class="chat-history-delete" onclick="event.stopPropagation(); app.deleteChatSession(${session.id})" title="删除此对话">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        }).join('');

        chatHistoryContainer.innerHTML = historyHtml;
    }

    formatChatTime(date) {
        const now = new Date();
        const diff = now - date;
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (hours < 1) return '刚刚';
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;

        return date.toLocaleDateString('zh-CN');
    }

    // 聊天菜单管理
    toggleChatMenu() {
        const menu = document.getElementById('chatDropdownMenu');
        if (menu) {
            menu.classList.toggle('active');
        }
    }

    closeChatMenu() {
        const menu = document.getElementById('chatDropdownMenu');
        if (menu) {
            menu.classList.remove('active');
        }
    }

    // 删除单个聊天会话
    deleteChatSession(sessionId) {
        if (!confirm('确定要删除这个对话吗？此操作不可撤销。')) {
            return;
        }

        try {
            // 从会话列表中移除
            this.chatSessions = this.chatSessions.filter(s => s.id !== sessionId);

            // 如果删除的是当前会话，创建新会话
            if (this.currentChatSession && this.currentChatSession.id === sessionId) {
                this.newChat();
            }

            // 保存到本地存储
            localStorage.setItem('chatSessions', JSON.stringify(this.chatSessions));

            // 重新渲染历史记录
            this.renderChatHistory();

            this.showToast('success', '删除成功', '对话已删除');

        } catch (error) {
            console.error('Failed to delete chat session:', error);
            this.showToast('error', '删除失败', '删除对话时发生错误');
        }
    }

    // 清空所有聊天历史
    clearAllChatHistory() {
        if (!confirm('确定要清空所有聊天历史吗？此操作不可撤销，将删除所有保存的对话记录。')) {
            return;
        }

        try {
            // 清空所有数据
            this.chatSessions = [];
            this.currentChatSession = null;
            this.chatHistory = [];

            // 清空本地存储
            localStorage.removeItem('chatSessions');

            // 重新渲染界面
            this.renderChatHistory();
            this.newChat();

            // 关闭菜单
            this.closeChatMenu();

            this.showToast('success', '清空完成', '所有聊天历史已清空');

        } catch (error) {
            console.error('Failed to clear chat history:', error);
            this.showToast('error', '清空失败', '清空聊天历史时发生错误');
        }
    }

    // 导出聊天历史
    exportChatHistory() {
        try {
            if (this.chatSessions.length === 0) {
                this.showToast('warning', '无数据', '没有聊天历史可以导出');
                return;
            }

            const exportData = {
                exportTime: new Date().toISOString(),
                version: '1.0',
                sessions: this.chatSessions
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `ai-cousin-chat-history-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            // 关闭菜单
            this.closeChatMenu();

            this.showToast('success', '导出成功', '聊天历史已导出到文件');

        } catch (error) {
            console.error('Failed to export chat history:', error);
            this.showToast('error', '导出失败', '导出聊天历史时发生错误');
        }
    }

    // 导入聊天历史
    importChatHistory() {
        try {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';

            input.onchange = (e) => {
                const file = e.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const importData = JSON.parse(e.target.result);

                        if (!importData.sessions || !Array.isArray(importData.sessions)) {
                            throw new Error('无效的文件格式');
                        }

                        // 确认导入
                        if (!confirm(`确定要导入 ${importData.sessions.length} 个对话吗？这将覆盖现有的聊天历史。`)) {
                            return;
                        }

                        // 转换日期字符串为Date对象
                        importData.sessions.forEach(session => {
                            session.createdAt = new Date(session.createdAt);
                            session.updatedAt = new Date(session.updatedAt);
                            session.messages.forEach(msg => {
                                msg.timestamp = new Date(msg.timestamp);
                            });
                        });

                        // 导入数据
                        this.chatSessions = importData.sessions;
                        localStorage.setItem('chatSessions', JSON.stringify(this.chatSessions));

                        // 重新渲染
                        this.renderChatHistory();

                        // 关闭菜单
                        this.closeChatMenu();

                        this.showToast('success', '导入成功', `已导入 ${importData.sessions.length} 个对话`);

                    } catch (error) {
                        console.error('Failed to import chat history:', error);
                        this.showToast('error', '导入失败', '文件格式错误或数据损坏');
                    }
                };
                reader.readAsText(file);
            };

            input.click();

        } catch (error) {
            console.error('Failed to import chat history:', error);
            this.showToast('error', '导入失败', '导入聊天历史时发生错误');
        }
    }

    async connectDatabase() {
        const dbType = document.getElementById('dbType').value;
        const host = document.getElementById('dbHost').value;
        const port = parseInt(document.getElementById('dbPort').value);
        const databaseName = document.getElementById('dbName').value;
        const username = document.getElementById('dbUsername').value;
        const password = document.getElementById('dbPassword').value;

        if (!host || !databaseName || !username) {
            this.showToast('warning', '参数不完整', '请填写必要的数据库连接信息');
            return;
        }

        this.showLoading('正在连接数据库...');

        try {
            const response = await fetch(`${this.apiBase}/database/connect`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    dbType,
                    host,
                    port: port || (dbType === 'mysql' ? 3306 : 5432),
                    databaseName,
                    username,
                    password
                })
            });

            const result = await response.json();
            
            if (result.status === 'success') {
                this.showToast('success', '连接成功', `成功连接到数据库，发现 ${result.tables.length} 个表`);
                this.renderDatabaseTables(result.tables);
                await this.loadStatistics(); // Update connected databases count
            } else {
                this.showToast('error', '连接失败', result.message || '数据库连接失败');
            }
        } catch (error) {
            console.error('Database connection error:', error);
            this.showToast('error', '连接失败', '网络错误，请检查连接信息');
        } finally {
            this.hideLoading();
        }
    }

    async testDatabaseConnection() {
        // This would be a separate API endpoint for testing connection
        this.showToast('info', '测试连接', '连接测试功能开发中...');
    }

    renderDatabaseTables(tables) {
        const tablesContainer = document.getElementById('databaseTables');
        if (!tablesContainer) return;

        if (tables.length === 0) {
            tablesContainer.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3>未发现表</h3>
                    <p>数据库中没有找到任何表</p>
                </div>
            `;
            return;
        }

        const tablesHtml = tables.map(table => `
            <div class="table-card" onclick="app.selectTable('${table}')">
                <div class="table-icon">
                    <i class="fas fa-table"></i>
                </div>
                <div class="table-info">
                    <h4>${table}</h4>
                    <p>点击选择此表</p>
                </div>
            </div>
        `).join('');

        tablesContainer.innerHTML = tablesHtml;
    }

    selectTable(tableName) {
        // Remove previous selection
        document.querySelectorAll('.table-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Add selection to clicked table
        event.target.closest('.table-card').classList.add('selected');
        
        this.showToast('info', '表已选择', `已选择表: ${tableName}`);
    }

    async loadSettings() {
        try {
            // Load embedding status
            const embeddingResponse = await fetch(`${this.apiBase}/embedding/status`);
            if (embeddingResponse.ok) {
                const embeddingData = await embeddingResponse.json();
                this.renderEmbeddingSettings(embeddingData);
            }

            // Load cache stats
            await this.refreshCacheStats();

            // Load performance stats
            await this.refreshPerformanceStats();

        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }

    renderEmbeddingSettings(embeddingData) {
        // Update external embedding toggle based on current status
        const externalToggle = document.getElementById('externalEmbeddingEnabled');
        const externalUrlInput = document.getElementById('externalEmbeddingUrl');

        if (externalToggle) {
            externalToggle.checked = embeddingData.isExternalAvailable || false;

            // 添加事件监听器
            externalToggle.addEventListener('change', (e) => {
                this.toggleExternalEmbedding(e.target.checked);
            });
        }

        if (externalUrlInput) {
            // 设置默认URL
            externalUrlInput.value = externalUrlInput.value || 'http://0.0.0.0:8000';

            // 添加事件监听器
            externalUrlInput.addEventListener('change', (e) => {
                this.updateExternalEmbeddingUrl(e.target.value);
            });
        }
    }

    async toggleExternalEmbedding(enabled) {
        try {
            console.log(`🔄 ${enabled ? '启用' : '禁用'}外部向量化服务...`);

            const url = document.getElementById('externalEmbeddingUrl').value;

            // 调用后端API更新配置
            const response = await fetch(`${this.apiBase}/embedding/external/config`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    enabled: enabled,
                    baseUrl: url
                })
            });

            const result = await response.json();

            if (result.status === 'info') {
                this.showToast('info', '配置更新', result.message);

                // 显示环境变量设置提示
                if (result.envVars) {
                    const envVarText = Object.entries(result.envVars)
                        .map(([key, value]) => `${key}=${value}`)
                        .join('\n');

                    console.log('环境变量设置:', envVarText);
                    this.showToast('info', '环境变量设置',
                        `请设置以下环境变量并重启服务:\n${envVarText}`);
                }
            } else {
                this.showToast('error', '配置失败', result.message || '配置更新失败');
            }

        } catch (error) {
            console.error('Failed to toggle external embedding:', error);
            this.showToast('error', '配置失败', `无法更新外部向量化服务配置: ${error.message}`);
        }
    }

    async updateExternalEmbeddingUrl(url) {
        try {
            console.log('🔄 更新外部向量化服务URL:', url);

            // 验证URL格式
            try {
                new URL(url);
            } catch (e) {
                this.showToast('warning', 'URL格式错误', '请输入有效的URL地址');
                return;
            }

            // 自动测试连接
            await this.testExternalEmbeddingService(url);

        } catch (error) {
            console.error('Failed to update external embedding URL:', error);
            this.showToast('error', '配置失败', '无法更新外部向量化服务URL');
        }
    }

    async testExternalEmbeddingService(url = null) {
        try {
            const testUrl = url || document.getElementById('externalEmbeddingUrl').value;

            if (!testUrl) {
                this.showToast('warning', '测试失败', '请先输入服务URL');
                return;
            }

            console.log('🔍 测试外部向量化服务:', testUrl);
            this.showLoading('正在测试外部向量化服务连接...');

            const response = await fetch(`${this.apiBase}/embedding/external/test`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    baseUrl: testUrl
                })
            });

            const result = await response.json();

            if (result.status === 'success') {
                this.showToast('success', '连接成功', result.message);
            } else if (result.status === 'warning') {
                this.showToast('warning', '连接警告', result.message);
            } else {
                this.showToast('error', '连接失败', result.message);
            }

        } catch (error) {
            console.error('Failed to test external embedding service:', error);
            this.showToast('error', '测试失败', `连接测试失败: ${error.message}`);
        } finally {
            this.hideLoading();
        }
    }

    async refreshCacheStats() {
        try {
            const response = await fetch(`${this.apiBase}/embedding/cache/stats`);
            if (response.ok) {
                const data = await response.json();
                this.renderCacheStats(data.cacheStats);
            }
        } catch (error) {
            console.error('Failed to load cache stats:', error);
        }
    }

    renderCacheStats(cacheStats) {
        const cacheStatsContainer = document.getElementById('cacheStats');
        if (!cacheStatsContainer || !cacheStats) return;

        cacheStatsContainer.innerHTML = `
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-label">缓存命中</div>
                    <div class="stat-value">${cacheStats.hits}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">缓存未命中</div>
                    <div class="stat-value">${cacheStats.misses}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">命中率</div>
                    <div class="stat-value">${cacheStats.hitRate}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">缓存大小</div>
                    <div class="stat-value">${cacheStats.size}</div>
                </div>
            </div>
        `;
    }

    async refreshPerformanceStats() {
        // This would call the performance metrics API
        const performanceStatsContainer = document.getElementById('performanceStats');
        if (performanceStatsContainer) {
            performanceStatsContainer.innerHTML = `
                <div class="performance-info">
                    <p>性能监控功能开发中...</p>
                </div>
            `;
        }
    }

    async clearCache() {
        if (!confirm('确定要清空所有缓存吗？')) {
            return;
        }

        try {
            const response = await fetch(`${this.apiBase}/embedding/cache/clear`, {
                method: 'POST'
            });

            if (response.ok) {
                this.showToast('success', '缓存清空', '所有缓存已成功清空');
                await this.refreshCacheStats();
            } else {
                this.showToast('error', '清空失败', '无法清空缓存');
            }
        } catch (error) {
            console.error('Failed to clear cache:', error);
            this.showToast('error', '清空失败', '网络错误，请重试');
        }
    }

    async refreshSystemStatus() {
        this.showLoading('正在刷新系统状态...');
        
        try {
            await this.loadSystemStatus();
            await this.loadStatistics();
            this.showToast('success', '刷新完成', '系统状态已更新');
        } catch (error) {
            this.showToast('error', '刷新失败', '无法刷新系统状态');
        } finally {
            this.hideLoading();
        }
    }

    async refreshDocuments() {
        this.showLoading('正在刷新文档列表...');
        
        try {
            await this.loadDocuments();
            this.showToast('success', '刷新完成', '文档列表已更新');
        } catch (error) {
            this.showToast('error', '刷新失败', '无法刷新文档列表');
        } finally {
            this.hideLoading();
        }
    }

    showLoading(message = '处理中...') {
        const overlay = document.getElementById('loadingOverlay');
        const text = document.getElementById('loadingText');
        
        if (text) text.textContent = message;
        overlay.classList.add('active');
    }

    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        overlay.classList.remove('active');
    }

    showToast(type, title, message, addToNotifications = true) {
        // 显示Toast
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        const iconMap = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };

        toast.innerHTML = `
            <div class="toast-icon">
                <i class="fas fa-${iconMap[type]}"></i>
            </div>
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add close functionality
        toast.querySelector('.toast-close').addEventListener('click', () => {
            toast.remove();
        });

        container.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 5000);

        // 同时添加到通知中心
        if (addToNotifications) {
            const persistent = type === 'error' || type === 'warning'; // 错误和警告保持更久
            this.addNotification(type, title, message, persistent);
        }
    }

    // 通知管理方法
    toggleNotificationPanel() {
        const panel = document.getElementById('notificationPanel');
        if (!panel) return;

        if (this.notificationPanelOpen) {
            this.closeNotificationPanel();
        } else {
            this.openNotificationPanel();
        }
    }

    openNotificationPanel() {
        const panel = document.getElementById('notificationPanel');
        if (!panel) return;

        panel.classList.add('active');
        this.notificationPanelOpen = true;

        // 标记所有通知为已读
        this.markAllNotificationsAsRead();
        this.renderNotifications();
    }

    closeNotificationPanel() {
        const panel = document.getElementById('notificationPanel');
        if (!panel) return;

        panel.classList.remove('active');
        this.notificationPanelOpen = false;
    }

    addNotification(type, title, message, persistent = false) {
        const notification = {
            id: Date.now() + Math.random(),
            type: type,
            title: title,
            message: message,
            timestamp: new Date(),
            read: false,
            persistent: persistent
        };

        this.notifications.unshift(notification);

        // 限制通知数量
        if (this.notifications.length > 50) {
            this.notifications = this.notifications.slice(0, 50);
        }

        this.updateNotificationCount();
        this.renderNotifications();

        // 如果不是持久通知，自动删除
        if (!persistent) {
            setTimeout(() => {
                this.removeNotification(notification.id);
            }, 30000); // 30秒后自动删除
        }
    }

    removeNotification(notificationId) {
        this.notifications = this.notifications.filter(n => n.id !== notificationId);
        this.updateNotificationCount();
        this.renderNotifications();
    }

    markAllNotificationsAsRead() {
        this.notifications.forEach(n => n.read = true);
        this.updateNotificationCount();
    }

    clearAllNotifications() {
        this.notifications = [];
        this.updateNotificationCount();
        this.renderNotifications();
        this.showToast('success', '清空完成', '所有通知已清空');
    }

    renderNotifications() {
        const content = document.getElementById('notificationContent');
        if (!content) return;

        if (this.notifications.length === 0) {
            content.innerHTML = `
                <div class="notification-empty">
                    <div class="empty-icon">
                        <i class="fas fa-bell-slash"></i>
                    </div>
                    <h4>暂无通知</h4>
                    <p>当有新的系统消息时会显示在这里</p>
                </div>
            `;
            return;
        }

        const notificationsHtml = this.notifications.map(notification => `
            <div class="notification-item ${!notification.read ? 'unread' : ''}"
                 onclick="app.markNotificationAsRead('${notification.id}')">
                <div class="notification-icon ${notification.type}">
                    <i class="fas fa-${this.getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-body">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-time">${this.formatNotificationTime(notification.timestamp)}</div>
                </div>
                <button class="notification-close" onclick="event.stopPropagation(); app.removeNotification('${notification.id}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');

        content.innerHTML = notificationsHtml;
    }

    markNotificationAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id == notificationId);
        if (notification) {
            notification.read = true;
            this.updateNotificationCount();
            this.renderNotifications();
        }
    }

    getNotificationIcon(type) {
        const iconMap = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return iconMap[type] || 'bell';
    }

    formatNotificationTime(timestamp) {
        const now = new Date();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;

        return timestamp.toLocaleDateString('zh-CN');
    }

    updateNotificationCount() {
        const unreadCount = this.notifications.filter(n => !n.read).length;
        const badge = document.getElementById('notificationBadge');
        if (badge) {
            badge.textContent = unreadCount;
            badge.style.display = unreadCount > 0 ? 'block' : 'none';
        }
    }
}

// Global functions for onclick handlers
function switchToSection(sectionId) {
    if (window.app) {
        window.app.switchToSection(sectionId);
    }
}

function refreshSystemStatus() {
    if (window.app) {
        window.app.refreshSystemStatus();
    }
}

function refreshDocuments() {
    if (window.app) {
        window.app.refreshDocuments();
    }
}

function newChat() {
    if (window.app) {
        window.app.newChat();
    }
}

function connectDatabase() {
    if (window.app) {
        window.app.connectDatabase();
    }
}

function testDatabaseConnection() {
    if (window.app) {
        window.app.testDatabaseConnection();
    }
}

function refreshCacheStats() {
    if (window.app) {
        window.app.refreshCacheStats();
    }
}

function clearCache() {
    if (window.app) {
        window.app.clearCache();
    }
}

function testExternalEmbeddingService() {
    if (window.app) {
        window.app.testExternalEmbeddingService();
    }
}

function runDiagnostics() {
    if (window.app) {
        window.app.runDiagnostics();
    }
}

// 通知相关的全局函数
function toggleNotificationPanel() {
    if (window.app) {
        window.app.toggleNotificationPanel();
    }
}

function clearAllNotifications() {
    if (window.app) {
        window.app.clearAllNotifications();
    }
}

function markNotificationAsRead(id) {
    if (window.app) {
        window.app.markNotificationAsRead(id);
    }
}

function removeNotification(id) {
    if (window.app) {
        window.app.removeNotification(id);
    }
}

// 聊天历史相关的全局函数
function loadChatSession(sessionId) {
    if (window.app) {
        window.app.loadChatSession(sessionId);
    }
}

function toggleChatMenu() {
    if (window.app) {
        window.app.toggleChatMenu();
    }
}

function deleteChatSession(sessionId) {
    if (window.app) {
        window.app.deleteChatSession(sessionId);
    }
}

function clearAllChatHistory() {
    if (window.app) {
        window.app.clearAllChatHistory();
    }
}

function exportChatHistory() {
    if (window.app) {
        window.app.exportChatHistory();
    }
}

function importChatHistory() {
    if (window.app) {
        window.app.importChatHistory();
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    window.app = new AiCousinProApp();
});
