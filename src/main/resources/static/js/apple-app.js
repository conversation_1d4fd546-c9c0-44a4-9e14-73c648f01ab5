/**
 * AI Cousin Pro - Apple Style Application
 * Modern, premium document intelligence platform
 */

class AiCousinPro {
    constructor() {
        this.apiBase = '/api/rag';
        this.databaseApiBase = '/api/database';
        this.currentTab = 'chat';
        this.documents = new Map();
        this.chatHistory = [];
        this.embeddingStatus = 'UNKNOWN';
        this.isProcessing = false;
        
        this.init();
    }

    async init() {
        console.log('🚀 AI Cousin Pro 初始化...');
        
        this.setupEventListeners();
        this.setupDragAndDrop();
        this.initializeTabs();
        
        // 延迟加载数据
        setTimeout(() => {
            this.loadSystemStatus();
            this.loadDocuments();
            this.updateStats();
        }, 500);
        
        console.log('✅ AI Cousin Pro 初始化完成');
    }

    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabId = e.target.dataset.tab;
                this.switchTab(tabId);
            });
        });

        // File upload
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                if (e.target.files[0]) {
                    this.handleFileUpload(e.target.files[0]);
                }
            });
        }

        // Chat functionality
        const sendButton = document.getElementById('sendButton');
        const chatInput = document.getElementById('chatInput');
        
        if (sendButton) {
            sendButton.addEventListener('click', () => this.sendMessage());
        }

        if (chatInput) {
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });

            // Auto-resize textarea
            chatInput.addEventListener('input', () => {
                chatInput.style.height = 'auto';
                chatInput.style.height = Math.min(chatInput.scrollHeight, 120) + 'px';
            });
        }

        // Similarity slider
        const similaritySlider = document.getElementById('similarityThreshold');
        const similarityValue = document.getElementById('similarityValue');
        
        if (similaritySlider && similarityValue) {
            similaritySlider.addEventListener('input', (e) => {
                const value = parseFloat(e.target.value);
                similarityValue.textContent = value.toFixed(2);
            });
        }

        // Database connection
        const connectBtn = document.querySelector('[onclick="connectDatabase()"]');
        if (connectBtn) {
            connectBtn.onclick = () => this.connectDatabase();
        }

        // Upload area click
        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            uploadArea.addEventListener('click', () => {
                document.getElementById('fileInput').click();
            });
        }
    }

    setupDragAndDrop() {
        const uploadArea = document.getElementById('uploadArea');
        if (!uploadArea) return;

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.add('dragover');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.remove('dragover');
            }, false);
        });

        uploadArea.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileUpload(files[0]);
            }
        }, false);
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    initializeTabs() {
        // Set initial active tab
        this.switchTab('chat');
    }

    switchTab(tabId) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabId}-tab`).classList.add('active');

        this.currentTab = tabId;

        // Load tab-specific data
        if (tabId === 'documents') {
            this.loadDocuments();
        } else if (tabId === 'database') {
            this.loadDatabaseTables();
        }
    }

    async handleFileUpload(file) {
        if (this.isProcessing) {
            this.showToast('warning', '处理中', '请等待当前文件处理完成');
            return;
        }

        // Validate file
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = ['.pdf', '.docx', '.txt', '.md'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

        if (file.size > maxSize) {
            this.showToast('error', '文件过大', '文件大小不能超过 10MB');
            return;
        }

        if (!allowedTypes.includes(fileExtension)) {
            this.showToast('error', '文件格式不支持', '请上传 PDF、DOCX、TXT 或 MD 格式的文件');
            return;
        }

        this.isProcessing = true;
        this.showLoading('正在上传和处理文档...');

        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch(`${this.apiBase}/documents`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.status === 'SUCCESS') {
                this.showToast('success', '上传成功', `文档 "${result.documentName}" 已成功处理`);
                this.loadDocuments();
                this.updateStats();
                
                // Switch to documents tab to show the uploaded file
                if (this.currentTab !== 'documents') {
                    this.switchTab('documents');
                }
            } else {
                this.showToast('error', '上传失败', result.message || '文档处理失败');
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.showToast('error', '上传失败', '网络错误，请重试');
        } finally {
            this.isProcessing = false;
            this.hideLoading();
        }
    }

    async sendMessage() {
        console.log('🎯 开始发送消息...');

        const chatInput = document.getElementById('chatInput');
        if (!chatInput) {
            console.error('❌ 找不到聊天输入框元素 #chatInput');
            return;
        }

        const message = chatInput.value.trim();
        console.log('📝 查询内容:', message);

        if (!message || this.isProcessing) {
            console.warn('⚠️ 查询内容为空或正在处理中');
            return;
        }

        this.isProcessing = true;
        chatInput.value = '';
        chatInput.style.height = 'auto';

        // Add user message to chat
        this.addMessageToChat('user', message);

        // Get query parameters
        const sourceTypeElement = document.getElementById('sourceType');
        const topKElement = document.getElementById('topK');
        const similarityElement = document.getElementById('similarityThreshold');

        if (!sourceTypeElement || !topKElement || !similarityElement) {
            console.error('❌ 找不到必要的控制元素');
            this.addMessageToChat('assistant', '抱歉，界面控件加载失败，请刷新页面重试。');
            this.isProcessing = false;
            return;
        }

        const sourceType = sourceTypeElement.value || 'all';
        const topK = parseInt(topKElement.value) || 5;
        const similarityThreshold = parseFloat(similarityElement.value) || 0.7;

        const queryRequest = {
            query: message,
            sourceType: sourceType,
            topK: topK,
            similarityThreshold: similarityThreshold
        };

        console.log('🎯 查询参数:', queryRequest);

        try {
            console.log('🚀 发送查询请求到:', `${this.apiBase}/query`);

            const response = await fetch(`${this.apiBase}/query`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(queryRequest)
            });

            console.log('📡 响应状态:', response.status, response.statusText);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('✅ 查询响应:', result);

            if (result.answer) {
                this.addMessageToChat('assistant', result.answer, result.retrievedChunks);

                // Update embedding status if provided
                if (result.embeddingStatus) {
                    this.updateEmbeddingStatus(result.embeddingStatus, result.usingFallbackEmbedding);
                }
            } else {
                console.warn('⚠️ 响应中没有answer字段:', result);
                this.addMessageToChat('assistant', '抱歉，我无法理解您的问题。请尝试重新表述。');
            }
        } catch (error) {
            console.error('❌ 查询错误:', error);
            this.addMessageToChat('assistant', `抱歉，查询时出现错误：${error.message}`);
        } finally {
            this.isProcessing = false;
        }
    }

    addMessageToChat(role, content, sources = null) {
        const chatMessages = document.getElementById('chatMessages');
        const welcomeMessage = chatMessages.querySelector('.welcome-message');
        
        // Remove welcome message on first interaction
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}-message`;
        
        const timestamp = new Date().toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });

        let sourcesHtml = '';
        if (sources && sources.length > 0) {
            sourcesHtml = `
                <div class="message-sources">
                    <div class="sources-header">
                        <i class="fas fa-link"></i>
                        <span>参考来源</span>
                    </div>
                    <div class="sources-list">
                        ${sources.map((source, index) => `
                            <div class="source-item">
                                <span class="source-index">${index + 1}</span>
                                <div class="source-info">
                                    <div class="source-name">${source.documentName || '未知文档'}</div>
                                    <div class="source-similarity">相似度: ${(source.similarity * 100).toFixed(1)}%</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-${role === 'user' ? 'user' : 'robot'}"></i>
            </div>
            <div class="message-content">
                <div class="message-header">
                    <span class="message-sender">${role === 'user' ? '您' : 'AI助手'}</span>
                    <span class="message-time">${timestamp}</span>
                </div>
                <div class="message-text">${this.formatMessage(content)}</div>
                ${sourcesHtml}
            </div>
        `;

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // Add to chat history
        this.chatHistory.push({ role, content, timestamp, sources });
    }

    formatMessage(content) {
        // Simple markdown-like formatting
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>');
    }

    async loadDocuments() {
        try {
            const response = await fetch(`${this.apiBase}/documents`);
            const data = await response.json();
            
            if (data.documents) {
                this.documents.clear();
                data.documents.forEach(doc => {
                    this.documents.set(doc.id, doc);
                });
                
                this.renderDocuments();
                this.updateDocumentCount(data.total || 0);
            }
        } catch (error) {
            console.error('Failed to load documents:', error);
        }
    }

    renderDocuments() {
        const documentsList = document.getElementById('documentsList');
        if (!documentsList) return;

        if (this.documents.size === 0) {
            documentsList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <h3>暂无文档</h3>
                    <p>上传您的第一个文档开始使用AI助手</p>
                </div>
            `;
            return;
        }

        const documentsHtml = Array.from(this.documents.values()).map(doc => `
            <div class="document-card" data-id="${doc.id}">
                <div class="document-header">
                    <div class="document-icon">
                        <i class="fas fa-${this.getFileIcon(doc.fileName)}"></i>
                    </div>
                    <div class="document-info">
                        <h4>${doc.fileName}</h4>
                        <div class="document-meta">
                            ${this.formatFileSize(doc.fileSize)} • ${this.formatDate(doc.uploadTime)}
                        </div>
                    </div>
                </div>
                <div class="document-body">
                    <div class="document-stats">
                        <div class="stat-badge">
                            <i class="fas fa-puzzle-piece"></i>
                            <span>${doc.chunksCount || 0} 块</span>
                        </div>
                        <div class="stat-badge">
                            <i class="fas fa-clock"></i>
                            <span>${doc.processingTime || 0}ms</span>
                        </div>
                    </div>
                    <div class="document-actions">
                        <button class="btn btn-small btn-outline" onclick="app.viewDocument('${doc.id}')">
                            <i class="fas fa-eye"></i>
                            查看
                        </button>
                        <button class="btn btn-small btn-secondary" onclick="app.deleteDocument('${doc.id}')">
                            <i class="fas fa-trash"></i>
                            删除
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        documentsList.innerHTML = documentsHtml;
    }

    getFileIcon(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        const iconMap = {
            'pdf': 'file-pdf',
            'docx': 'file-word',
            'doc': 'file-word',
            'txt': 'file-alt',
            'md': 'file-code'
        };
        return iconMap[extension] || 'file';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDate(timestamp) {
        return new Date(timestamp).toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    async deleteDocument(documentId) {
        if (!confirm('确定要删除这个文档吗？此操作不可撤销。')) {
            return;
        }

        try {
            const response = await fetch(`${this.apiBase}/documents/${documentId}`, {
                method: 'DELETE'
            });

            const result = await response.json();
            
            if (result.status === 'SUCCESS') {
                this.showToast('success', '删除成功', '文档已成功删除');
                this.loadDocuments();
                this.updateStats();
            } else {
                this.showToast('error', '删除失败', result.message || '删除文档时发生错误');
            }
        } catch (error) {
            console.error('Delete error:', error);
            this.showToast('error', '删除失败', '网络错误，请重试');
        }
    }

    async connectDatabase() {
        const dbType = document.getElementById('dbType').value;
        const host = document.getElementById('dbHost').value;
        const port = parseInt(document.getElementById('dbPort').value);
        const databaseName = document.getElementById('dbName').value;
        const username = document.getElementById('dbUsername').value;
        const password = document.getElementById('dbPassword').value;

        if (!host || !databaseName || !username) {
            this.showToast('warning', '参数不完整', '请填写必要的数据库连接信息');
            return;
        }

        this.showLoading('正在连接数据库...');

        try {
            const response = await fetch(`${this.databaseApiBase}/connect`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    dbType,
                    host,
                    port: port || (dbType === 'mysql' ? 3306 : 5432),
                    databaseName,
                    username,
                    password
                })
            });

            const result = await response.json();
            
            if (result.status === 'success') {
                this.showToast('success', '连接成功', `成功连接到数据库，发现 ${result.tables.length} 个表`);
                this.renderDatabaseTables(result.tables);
            } else {
                this.showToast('error', '连接失败', result.message || '数据库连接失败');
            }
        } catch (error) {
            console.error('Database connection error:', error);
            this.showToast('error', '连接失败', '网络错误，请检查连接信息');
        } finally {
            this.hideLoading();
        }
    }

    renderDatabaseTables(tables) {
        const tablesContainer = document.getElementById('databaseTables');
        if (!tablesContainer) return;

        if (tables.length === 0) {
            tablesContainer.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3>未发现表</h3>
                    <p>数据库中没有找到任何表</p>
                </div>
            `;
            return;
        }

        const tablesHtml = tables.map(table => `
            <div class="table-card" onclick="app.selectTable('${table}')">
                <div class="table-name">${table}</div>
                <div class="table-info">点击选择此表</div>
            </div>
        `).join('');

        tablesContainer.innerHTML = tablesHtml;
    }

    selectTable(tableName) {
        // Remove previous selection
        document.querySelectorAll('.table-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Add selection to clicked table
        event.target.closest('.table-card').classList.add('selected');
        
        this.showToast('info', '表已选择', `已选择表: ${tableName}`);
    }

    async loadSystemStatus() {
        try {
            const [healthResponse, embeddingResponse] = await Promise.all([
                fetch(`${this.apiBase}/health`),
                fetch(`${this.apiBase}/embedding/status`)
            ]);

            if (healthResponse.ok) {
                this.updateSystemStatus('正常');
            } else {
                this.updateSystemStatus('异常');
            }

            if (embeddingResponse.ok) {
                const embeddingData = await embeddingResponse.json();
                this.updateEmbeddingStatus(embeddingData.status, embeddingData.usingFallback);
            }
        } catch (error) {
            console.error('Failed to load system status:', error);
            this.updateSystemStatus('未知');
        }
    }

    updateSystemStatus(status) {
        const statusElement = document.getElementById('systemStatus');
        if (statusElement) {
            statusElement.textContent = status;
            statusElement.className = `status-value ${status === '正常' ? 'status-success' : 'status-error'}`;
        }
    }

    updateEmbeddingStatus(status, usingFallback) {
        const statusElement = document.getElementById('embeddingStatus');
        if (statusElement) {
            const displayStatus = usingFallback ? 'Fallback模式' : status;
            statusElement.textContent = displayStatus;
            statusElement.className = `status-value ${usingFallback ? 'status-warning' : 'status-success'}`;
        }
        this.embeddingStatus = status;
    }

    updateDocumentCount(count) {
        const countElement = document.getElementById('documentCount');
        if (countElement) {
            countElement.textContent = count;
        }
    }

    updateStats() {
        // Update hero stats with animation
        const documentsCountElement = document.getElementById('documentsCount');
        const queriesCountElement = document.getElementById('queriesCount');
        
        if (documentsCountElement) {
            this.animateNumber(documentsCountElement, this.documents.size);
        }
        
        if (queriesCountElement) {
            this.animateNumber(queriesCountElement, this.chatHistory.length);
        }
    }

    animateNumber(element, targetNumber) {
        const startNumber = parseInt(element.textContent) || 0;
        const duration = 1000;
        const startTime = Date.now();
        
        const updateNumber = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const currentNumber = Math.floor(startNumber + (targetNumber - startNumber) * progress);
            
            element.textContent = currentNumber;
            
            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        };
        
        updateNumber();
    }

    showLoading(message = '处理中...') {
        const overlay = document.getElementById('loadingOverlay');
        const text = overlay.querySelector('.loading-text');
        
        if (text) text.textContent = message;
        overlay.classList.add('active');
    }

    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        overlay.classList.remove('active');
    }

    showToast(type, title, message) {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        const iconMap = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        
        toast.innerHTML = `
            <div class="toast-icon">
                <i class="fas fa-${iconMap[type]}"></i>
            </div>
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Add close functionality
        toast.querySelector('.toast-close').addEventListener('click', () => {
            toast.remove();
        });
        
        container.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 5000);
    }
}

// Global functions for onclick handlers
function scrollToSection(sectionId) {
    document.getElementById(sectionId).scrollIntoView({ 
        behavior: 'smooth' 
    });
}

function playDemo() {
    app.showToast('info', '演示功能', '演示视频功能即将推出');
}

function newChat() {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.innerHTML = `
        <div class="welcome-message">
            <div class="welcome-icon">
                <i class="fas fa-robot"></i>
            </div>
            <h3>新的对话</h3>
            <p>您好！我是您的开源智能文档助手，完全免费使用。有什么可以帮助您的？</p>
        </div>
    `;
    app.chatHistory = [];
}

// Initialize the application
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new AiCousinPro();
    window.app = app; // Make it globally accessible
});
