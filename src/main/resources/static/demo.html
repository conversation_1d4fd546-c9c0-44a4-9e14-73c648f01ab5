<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Cousin - 全新Apple风格设计</title>
    <link rel="stylesheet" href="css/apple-style.css?v=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Demo specific styles */
        .demo-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(90deg, #007AFF, #5AC8FA);
            color: white;
            text-align: center;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            z-index: 1001;
            animation: slideDown 0.5s ease;
        }
        
        @keyframes slideDown {
            from { transform: translateY(-100%); }
            to { transform: translateY(0); }
        }
        
        .demo-banner a {
            color: white;
            text-decoration: underline;
            margin-left: 8px;
        }
        
        /* Adjust navbar for demo banner */
        .navbar {
            top: 40px;
        }
        
        .hero {
            padding-top: calc(var(--header-height) + 40px + var(--space-20));
        }
        
        /* Demo interaction hints */
        .demo-hint {
            position: absolute;
            background: rgba(0, 122, 255, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            pointer-events: none;
            z-index: 1000;
            animation: pulse 2s ease-in-out infinite;
        }
        
        .demo-hint::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: rgba(0, 122, 255, 0.9);
        }
        
        .hint-upload {
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
        }
        
        .hint-chat {
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        /* Enhanced animations for demo */
        .floating-card {
            animation-duration: 4s;
        }
        
        .orb-inner {
            animation-duration: 4s;
        }
        
        .ring {
            animation-duration: 3s;
        }
    </style>
</head>
<body>
    <!-- Demo Banner -->
    <div class="demo-banner">
        🎉 全新Apple风格设计演示 - 开源免费的智能文档助手
        <a href="index.html">返回原版本</a>
    </div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <div class="brand-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <span class="brand-text">AI Cousin</span>
            </div>
            <div class="nav-menu">
                <a href="#home" class="nav-link active">首页</a>
                <a href="#features" class="nav-link">功能</a>
                <a href="#workspace" class="nav-link">工作台</a>
                <a href="https://github.com/ai-cousin" class="nav-link">GitHub</a>
            </div>
            <div class="nav-actions">
                <button class="btn btn-primary" onclick="scrollToSection('workspace')">开始使用</button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <span class="badge-icon">🚀</span>
                    <span>开源免费的智能文档助手</span>
                </div>
                <h1 class="hero-title">
                    让您的文档
                    <span class="gradient-text">智能化</span>
                </h1>
                <p class="hero-subtitle">
                    基于先进的RAG技术，AI Cousin能够理解您的文档内容，
                    提供精准的问答服务。完全开源免费，让知识管理变得前所未有的简单。
                </p>
                <div class="hero-actions">
                    <button class="btn btn-primary btn-large" onclick="scrollToSection('workspace')">
                        <i class="fas fa-rocket"></i>
                        立即体验
                    </button>
                    <button class="btn btn-secondary btn-large" onclick="playDemo()">
                        <i class="fas fa-play"></i>
                        观看演示
                    </button>
                </div>
                <div class="hero-stats">
                    <div class="stat-item">
                        <div class="stat-number" id="documentsCount">156</div>
                        <div class="stat-label">已处理文档</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="queriesCount">2847</div>
                        <div class="stat-label">智能问答</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">开源</div>
                        <div class="stat-label">完全免费</div>
                    </div>
                </div>
            </div>
            <div class="hero-visual">
                <div class="visual-container">
                    <div class="floating-elements">
                        <div class="floating-card document-card">
                            <i class="fas fa-file-pdf"></i>
                            <span>PDF</span>
                        </div>
                        <div class="floating-card chat-card">
                            <i class="fas fa-comments"></i>
                            <span>智能问答</span>
                        </div>
                        <div class="floating-card brain-card">
                            <i class="fas fa-brain"></i>
                            <span>AI分析</span>
                        </div>
                    </div>
                    <div class="central-orb">
                        <div class="orb-inner">
                            <i class="fas fa-magic"></i>
                        </div>
                        <div class="orb-rings">
                            <div class="ring ring-1"></div>
                            <div class="ring ring-2"></div>
                            <div class="ring ring-3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">强大功能</h2>
                <p class="section-subtitle">为您的文档管理提供全方位的AI支持</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-upload"></i>
                    </div>
                    <h3 class="feature-title">智能文档上传</h3>
                    <p class="feature-description">支持PDF、Word、文本等多种格式，自动解析和向量化处理</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3 class="feature-title">语义搜索</h3>
                    <p class="feature-description">基于语义理解的智能搜索，找到最相关的内容片段</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="feature-title">智能问答</h3>
                    <p class="feature-description">基于文档内容的精准问答，提供详细的引用来源</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3 class="feature-title">数据库集成</h3>
                    <p class="feature-description">连接数据库，理解表结构，提供数据相关的智能问答</p>
                </div>
            </div>
        </div>
    </section>



    <!-- Demo Hints -->
    <div class="demo-hint hint-upload">
        点击体验文档上传 →
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <script>
        // Demo specific JavaScript
        function scrollToSection(sectionId) {
            document.getElementById(sectionId).scrollIntoView({ 
                behavior: 'smooth' 
            });
        }

        function playDemo() {
            showToast('info', '演示功能', '演示视频功能即将推出，敬请期待！');
        }

        function showToast(type, title, message) {
            const container = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            
            const iconMap = {
                success: 'check-circle',
                error: 'exclamation-circle',
                warning: 'exclamation-triangle',
                info: 'info-circle'
            };
            
            toast.innerHTML = `
                <div class="toast-icon">
                    <i class="fas fa-${iconMap[type]}"></i>
                </div>
                <div class="toast-content">
                    <div class="toast-title">${title}</div>
                    <div class="toast-message">${message}</div>
                </div>
                <button class="toast-close">
                    <i class="fas fa-times"></i>
                </button>
            `;
            
            toast.querySelector('.toast-close').addEventListener('click', () => {
                toast.remove();
            });
            
            container.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 5000);
        }

        // Demo interactions
        document.addEventListener('DOMContentLoaded', () => {
            // Show welcome toast
            setTimeout(() => {
                showToast('success', '欢迎体验', '这是全新的Apple风格设计，开源免费的智能文档助手！');
            }, 1000);

            // Animate stats
            setTimeout(() => {
                animateNumber(document.getElementById('documentsCount'), 156);
                animateNumber(document.getElementById('queriesCount'), 2847);
            }, 2000);

            // Remove demo hints after some time
            setTimeout(() => {
                document.querySelectorAll('.demo-hint').forEach(hint => {
                    hint.style.opacity = '0';
                    setTimeout(() => hint.remove(), 500);
                });
            }, 10000);
        });

        function animateNumber(element, targetNumber) {
            const startNumber = 0;
            const duration = 2000;
            const startTime = Date.now();
            
            const updateNumber = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const currentNumber = Math.floor(startNumber + (targetNumber - startNumber) * progress);
                
                element.textContent = currentNumber;
                
                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                }
            };
            
            updateNumber();
        }

        // Smooth scrolling for navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    </script>
</body>
</html>
