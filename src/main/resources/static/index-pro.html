<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Cousin Pro - 智能文档助手</title>
    <link rel="stylesheet" href="css/pro-style.css?v=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <div class="brand-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <span class="brand-text">AI Cousin Pro</span>
            </div>
            <div class="nav-menu">
                <a href="#dashboard" class="nav-link active" data-section="dashboard">
                    <i class="fas fa-home"></i>
                    <span>仪表板</span>
                </a>
                <a href="#documents" class="nav-link" data-section="documents">
                    <i class="fas fa-file-alt"></i>
                    <span>文档管理</span>
                </a>
                <a href="#chat" class="nav-link" data-section="chat">
                    <i class="fas fa-comments"></i>
                    <span>智能问答</span>
                </a>
                <a href="#database" class="nav-link" data-section="database">
                    <i class="fas fa-database"></i>
                    <span>数据库</span>
                </a>
                <a href="#settings" class="nav-link" data-section="settings">
                    <i class="fas fa-cog"></i>
                    <span>设置</span>
                </a>
            </div>
            <div class="nav-actions">
                <button class="btn btn-icon" id="themeToggle" title="切换主题">
                    <i class="fas fa-moon"></i>
                </button>
                <button class="btn btn-icon" id="notificationBtn" title="通知">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge" id="notificationBadge">0</span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Dashboard Section -->
        <section id="dashboard" class="content-section active">
            <div class="section-header">
                <h1 class="section-title">仪表板</h1>
                <p class="section-subtitle">系统概览和快速操作</p>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalDocuments">0</div>
                        <div class="stat-label">文档总数</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalQueries">0</div>
                        <div class="stat-label">查询次数</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="connectedDatabases">0</div>
                        <div class="stat-label">连接数据库</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="cacheHitRate">0%</div>
                        <div class="stat-label">缓存命中率</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h2 class="section-subtitle">快速操作</h2>
                <div class="actions-grid">
                    <button class="action-card" onclick="switchToSection('documents')">
                        <div class="action-icon">
                            <i class="fas fa-upload"></i>
                        </div>
                        <div class="action-content">
                            <h3>上传文档</h3>
                            <p>添加新的文档到知识库</p>
                        </div>
                    </button>
                    <button class="action-card" onclick="switchToSection('chat')">
                        <div class="action-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="action-content">
                            <h3>开始对话</h3>
                            <p>与AI助手进行智能问答</p>
                        </div>
                    </button>
                    <button class="action-card" onclick="switchToSection('database')">
                        <div class="action-icon">
                            <i class="fas fa-plug"></i>
                        </div>
                        <div class="action-content">
                            <h3>连接数据库</h3>
                            <p>集成外部数据源</p>
                        </div>
                    </button>
                    <button class="action-card" onclick="refreshSystemStatus()">
                        <div class="action-icon">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="action-content">
                            <h3>刷新状态</h3>
                            <p>更新系统运行状态</p>
                        </div>
                    </button>
                    <button class="action-card" onclick="runDiagnostics()">
                        <div class="action-icon">
                            <i class="fas fa-stethoscope"></i>
                        </div>
                        <div class="action-content">
                            <h3>系统诊断</h3>
                            <p>检查文档和查询功能</p>
                        </div>
                    </button>
                </div>
            </div>

            <!-- System Status -->
            <div class="system-status">
                <h2 class="section-subtitle">系统状态</h2>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-header">
                            <i class="fas fa-server"></i>
                            <span>服务状态</span>
                        </div>
                        <div class="status-value" id="systemStatus">
                            <span class="status-indicator"></span>
                            <span class="status-text">检查中...</span>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-header">
                            <i class="fas fa-vector-square"></i>
                            <span>向量化服务</span>
                        </div>
                        <div class="status-value" id="embeddingStatus">
                            <span class="status-indicator"></span>
                            <span class="status-text">检查中...</span>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-header">
                            <i class="fas fa-database"></i>
                            <span>Milvus数据库</span>
                        </div>
                        <div class="status-value" id="milvusStatus">
                            <span class="status-indicator"></span>
                            <span class="status-text">检查中...</span>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-header">
                            <i class="fas fa-memory"></i>
                            <span>缓存系统</span>
                        </div>
                        <div class="status-value" id="cacheStatus">
                            <span class="status-indicator"></span>
                            <span class="status-text">检查中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Documents Section -->
        <section id="documents" class="content-section">
            <div class="section-header">
                <h1 class="section-title">文档管理</h1>
                <p class="section-subtitle">上传、管理和组织您的文档</p>
            </div>

            <!-- Upload Area -->
            <div class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h3>拖拽文件到此处或点击上传</h3>
                        <p>支持 PDF、DOCX、TXT、MD 格式，最大 10MB</p>
                        <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                            <i class="fas fa-plus"></i>
                            选择文件
                        </button>
                    </div>
                    <input type="file" id="fileInput" accept=".pdf,.docx,.txt,.md" style="display: none;">
                    <input type="file" id="chatImportInput" accept=".json" style="display: none;">
                </div>
            </div>

            <!-- Documents List -->
            <div class="documents-section">
                <div class="documents-header">
                    <h2 class="section-subtitle">文档列表</h2>
                    <div class="documents-actions">
                        <button class="btn btn-outline" onclick="refreshDocuments()">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="搜索文档..." id="documentSearch">
                        </div>
                    </div>
                </div>
                <div class="documents-list" id="documentsList">
                    <!-- Documents will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Chat Section -->
        <section id="chat" class="content-section">
            <div class="chat-container">
                <div class="chat-sidebar">
                    <div class="chat-header">
                        <h2>对话历史</h2>
                        <div class="chat-header-actions">
                            <button class="btn btn-icon" onclick="newChat()" title="新建对话">
                                <i class="fas fa-plus"></i>
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-icon dropdown-toggle" onclick="toggleChatMenu()" title="更多选项">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div class="dropdown-menu" id="chatDropdownMenu">
                                    <button class="dropdown-item" onclick="clearAllChatHistory()">
                                        <i class="fas fa-trash"></i>
                                        清空所有历史
                                    </button>
                                    <button class="dropdown-item" onclick="exportChatHistory()">
                                        <i class="fas fa-download"></i>
                                        导出历史记录
                                    </button>
                                    <button class="dropdown-item" onclick="importChatHistory()">
                                        <i class="fas fa-upload"></i>
                                        导入历史记录
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="chat-history" id="chatHistory">
                        <!-- Chat history will be loaded here -->
                    </div>
                </div>
                
                <div class="chat-main">
                    <div class="chat-controls">
                        <div class="control-group">
                            <label>数据源</label>
                            <select id="sourceType" class="control-select">
                                <option value="all">全部</option>
                                <option value="documents">文档</option>
                                <option value="database">数据库</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label>检索数量</label>
                            <select id="topK" class="control-select">
                                <option value="3">3</option>
                                <option value="5" selected>5</option>
                                <option value="10">10</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label>相似度阈值</label>
                            <div class="slider-container">
                                <input type="range" id="similarityThreshold" class="control-slider" 
                                       min="0" max="1" step="0.1" value="0.3">
                                <span class="slider-value" id="similarityValue">0.3</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="chat-messages" id="chatMessages">
                        <div class="welcome-message">
                            <div class="welcome-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <h3>欢迎使用AI Cousin Pro</h3>
                            <p>我是您的智能文档助手，可以帮您分析文档内容、回答问题。请先上传文档或连接数据库，然后开始提问。</p>
                        </div>
                    </div>
                    
                    <div class="chat-input-area">
                        <div class="input-container">
                            <textarea id="chatInput" placeholder="输入您的问题..." rows="1"></textarea>
                            <button class="send-btn" id="sendButton">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Database Section -->
        <section id="database" class="content-section">
            <div class="section-header">
                <h1 class="section-title">数据库集成</h1>
                <p class="section-subtitle">连接和管理外部数据源</p>
            </div>

            <!-- Database Connection Form -->
            <div class="database-form">
                <h2 class="form-title">数据库连接</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>数据库类型</label>
                        <select id="dbType" class="form-input">
                            <option value="mysql">MySQL</option>
                            <option value="postgresql">PostgreSQL</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>主机地址</label>
                        <input type="text" id="dbHost" class="form-input" placeholder="localhost">
                    </div>
                    <div class="form-group">
                        <label>端口</label>
                        <input type="number" id="dbPort" class="form-input" placeholder="3306">
                    </div>
                    <div class="form-group">
                        <label>数据库名</label>
                        <input type="text" id="dbName" class="form-input" placeholder="database_name">
                    </div>
                    <div class="form-group">
                        <label>用户名</label>
                        <input type="text" id="dbUsername" class="form-input" placeholder="username">
                    </div>
                    <div class="form-group">
                        <label>密码</label>
                        <input type="password" id="dbPassword" class="form-input" placeholder="password">
                    </div>
                </div>
                <div class="form-actions">
                    <button class="btn btn-outline" onclick="testDatabaseConnection()">
                        <i class="fas fa-plug"></i>
                        测试连接
                    </button>
                    <button class="btn btn-primary" onclick="connectDatabase()">
                        <i class="fas fa-link"></i>
                        连接数据库
                    </button>
                </div>
            </div>

            <!-- Database Tables -->
            <div class="database-tables-section">
                <h2 class="section-subtitle">数据库表</h2>
                <div class="database-tables" id="databaseTables">
                    <!-- Tables will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Settings Section -->
        <section id="settings" class="content-section">
            <div class="section-header">
                <h1 class="section-title">系统设置</h1>
                <p class="section-subtitle">配置和管理系统参数</p>
            </div>

            <!-- Settings Content -->
            <div class="settings-content">
                <!-- Embedding Settings -->
                <div class="settings-group">
                    <h2 class="settings-title">向量化设置</h2>
                    <div class="settings-grid">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h3>外部向量化服务</h3>
                                <p>启用外部BGE模型服务</p>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" id="externalEmbeddingEnabled">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-info">
                                <h3>服务地址</h3>
                                <p>外部向量化服务的URL</p>
                            </div>
                            <div class="setting-control">
                                <div style="display: flex; gap: 8px; align-items: center;">
                                    <input type="text" id="externalEmbeddingUrl" class="form-input"
                                           placeholder="http://0.0.0.0:8000" style="flex: 1;">
                                    <button class="btn btn-outline btn-small" onclick="app.testExternalEmbeddingService()" title="测试连接">
                                        <i class="fas fa-plug"></i>
                                        测试
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cache Settings -->
                <div class="settings-group">
                    <h2 class="settings-title">缓存设置</h2>
                    <div class="cache-stats" id="cacheStats">
                        <!-- Cache stats will be loaded here -->
                    </div>
                    <div class="settings-actions">
                        <button class="btn btn-outline" onclick="refreshCacheStats()">
                            <i class="fas fa-sync-alt"></i>
                            刷新统计
                        </button>
                        <button class="btn btn-danger" onclick="clearCache()">
                            <i class="fas fa-trash"></i>
                            清空缓存
                        </button>
                    </div>
                </div>

                <!-- Performance Settings -->
                <div class="settings-group">
                    <h2 class="settings-title">性能监控</h2>
                    <div class="performance-stats" id="performanceStats">
                        <!-- Performance stats will be loaded here -->
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p class="loading-text" id="loadingText">处理中...</p>
        </div>
    </div>

    <!-- Notification Panel -->
    <div class="notification-panel" id="notificationPanel">
        <div class="notification-header">
            <h3>通知中心</h3>
            <div class="notification-actions">
                <button class="btn btn-icon btn-small" onclick="app.clearAllNotifications()" title="清空所有">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="btn btn-icon btn-small" onclick="app.toggleNotificationPanel()" title="关闭">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="notification-content" id="notificationContent">
            <div class="notification-empty">
                <div class="empty-icon">
                    <i class="fas fa-bell-slash"></i>
                </div>
                <h4>暂无通知</h4>
                <p>当有新的系统消息时会显示在这里</p>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- Scripts -->
    <script src="js/pro-app.js?v=1.0"></script>
</body>
</html>
