/* AI Cousin Pro - Apple Style CSS */

/* CSS Variables */
:root {
    /* Colors */
    --primary-blue: #007AFF;
    --primary-blue-hover: #0056CC;
    --primary-blue-light: rgba(0, 122, 255, 0.1);
    --primary-blue-dark: #004085;
    
    --success: #34C759;
    --warning: #FF9500;
    --error: #FF3B30;
    --info: #5AC8FA;
    
    /* Backgrounds */
    --bg-primary: #FFFFFF;
    --bg-secondary: #F9F9F9;
    --bg-tertiary: #F2F2F7;
    --bg-elevated: #FFFFFF;
    --bg-glass: rgba(255, 255, 255, 0.8);
    --bg-overlay: rgba(0, 0, 0, 0.4);
    
    /* Text Colors */
    --text-primary: #1D1D1F;
    --text-secondary: #86868B;
    --text-tertiary: #A1A1A6;
    --text-inverse: #FFFFFF;
    
    /* Borders */
    --border-light: #E5E5E7;
    --border-medium: #D1D1D6;
    --border-dark: #A1A1A6;
    
    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-2xl: 20px;
    --radius-full: 9999px;
    
    /* Spacing */
    --space-1: 4px;
    --space-2: 8px;
    --space-3: 12px;
    --space-4: 16px;
    --space-5: 20px;
    --space-6: 24px;
    --space-8: 32px;
    --space-10: 40px;
    --space-12: 48px;
    --space-16: 64px;
    --space-20: 80px;
    --space-24: 96px;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    
    --text-xs: 12px;
    --text-sm: 14px;
    --text-base: 16px;
    --text-lg: 18px;
    --text-xl: 20px;
    --text-2xl: 24px;
    --text-3xl: 32px;
    --text-4xl: 48px;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Layout */
    --navbar-height: 64px;
    --sidebar-width: 280px;
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-primary: #1C1C1E;
    --bg-secondary: #2C2C2E;
    --bg-tertiary: #3A3A3C;
    --bg-elevated: #2C2C2E;
    --bg-glass: rgba(28, 28, 30, 0.8);
    
    --text-primary: #FFFFFF;
    --text-secondary: #98989D;
    --text-tertiary: #636366;
    
    --border-light: #38383A;
    --border-medium: #48484A;
    --border-dark: #636366;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--navbar-height);
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-light);
    z-index: 1000;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-weight: var(--font-bold);
    font-size: var(--text-lg);
    color: var(--text-primary);
}

.brand-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--primary-blue), var(--info));
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--text-base);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-lg);
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: var(--font-medium);
    font-size: var(--text-sm);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.nav-link:hover {
    background: var(--primary-blue-light);
    color: var(--primary-blue);
}

.nav-link.active {
    background: var(--primary-blue);
    color: white;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    border: none;
    border-radius: var(--radius-md);
    font-family: var(--font-family);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    outline: none;
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-blue);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-blue-hover);
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-blue);
    border: 1px solid var(--primary-blue);
}

.btn-outline:hover {
    background: var(--primary-blue);
    color: white;
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background: var(--border-medium);
}

.btn-danger {
    background: var(--error);
    color: white;
}

.btn-danger:hover {
    background: #E60026;
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    position: relative;
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: var(--error);
    color: white;
    font-size: 10px;
    font-weight: var(--font-bold);
    padding: 2px 6px;
    border-radius: var(--radius-full);
    min-width: 16px;
    text-align: center;
}

/* Main Content */
.main-content {
    margin-top: var(--navbar-height);
    min-height: calc(100vh - var(--navbar-height));
    background: var(--bg-secondary);
}

.content-section {
    display: none;
    padding: var(--space-8);
    max-width: 1400px;
    margin: 0 auto;
    height: calc(100vh - var(--navbar-height)); /* 设置固定高度 */
    overflow-y: auto; /* 允许整体滚动 */
}

.content-section.active {
    display: block;
}

/* 聊天页面特殊处理 */
.content-section#chat {
    overflow: hidden; /* 聊天页面不允许整体滚动 */
    display: flex;
    flex-direction: column;
}

.content-section#chat .section-header {
    flex-shrink: 0;
}

.content-section#chat .chat-container {
    flex: 1;
    margin-top: var(--space-4);
}

.section-header {
    margin-bottom: var(--space-8);
}

.section-title {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.section-subtitle {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    margin-bottom: var(--space-6);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-12);
}

.stat-card {
    background: var(--bg-elevated);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    border: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    gap: var(--space-4);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-blue), var(--info));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--text-lg);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

/* Quick Actions */
.quick-actions {
    margin-bottom: var(--space-12);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
}

.action-card {
    background: var(--bg-elevated);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    display: flex;
    align-items: center;
    gap: var(--space-4);
    cursor: pointer;
    transition: all var(--transition-normal);
    text-align: left;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-blue);
}

.action-icon {
    width: 48px;
    height: 48px;
    background: var(--primary-blue-light);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-blue);
    font-size: var(--text-lg);
}

.action-content h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.action-content p {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0;
}

/* System Status */
.system-status {
    margin-bottom: var(--space-12);
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-4);
}

.status-item {
    background: var(--bg-elevated);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-5);
}

.status-header {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-3);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-secondary);
}

.status-value {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--border-medium);
}

.status-indicator.online {
    background: var(--success);
    animation: pulse 2s ease-in-out infinite;
}

.status-indicator.offline {
    background: var(--error);
}

.status-indicator.warning {
    background: var(--warning);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.status-text {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
}

/* Upload Section */
.upload-section {
    margin-bottom: var(--space-8);
}

.upload-area {
    background: var(--bg-elevated);
    border: 2px dashed var(--border-medium);
    border-radius: var(--radius-xl);
    padding: var(--space-12);
    text-align: center;
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(0, 122, 255, 0.02) 50%, transparent 70%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.upload-area:hover::before,
.upload-area.dragover::before {
    opacity: 1;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--primary-blue);
    background: var(--primary-blue-light);
    transform: translateY(-2px);
}

.upload-icon {
    font-size: var(--text-4xl);
    color: var(--primary-blue);
    margin-bottom: var(--space-4);
}

.upload-content h3 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.upload-content p {
    color: var(--text-secondary);
    margin-bottom: var(--space-6);
}

/* Documents Section */
.documents-section {
    background: var(--bg-elevated);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.documents-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-glass);
}

.documents-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: var(--space-3);
    color: var(--text-tertiary);
    font-size: var(--text-sm);
}

.search-box input {
    padding: var(--space-2) var(--space-3) var(--space-2) var(--space-8);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--bg-elevated);
    color: var(--text-primary);
    font-size: var(--text-sm);
    outline: none;
    transition: all var(--transition-fast);
    width: 200px;
}

.search-box input:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px var(--primary-blue-light);
}

.documents-list {
    padding: var(--space-6);
    min-height: 200px;
}

/* Chat Section */
.chat-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    height: calc(100vh - var(--navbar-height) - var(--space-16));
    max-height: 800px; /* 设置最大高度 */
    min-height: 600px; /* 设置最小高度 */
    background: var(--bg-elevated);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.chat-sidebar {
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-light);
    display: flex;
    flex-direction: column;
    height: 100%; /* 确保占满容器高度 */
    overflow: hidden; /* 防止整体滚动 */
}

.chat-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h2 {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
}

.chat-header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

/* Dropdown Menu */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 180px;
    background: var(--bg-elevated);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
    padding: var(--space-2);
    z-index: 1000;
    display: none;
    margin-top: var(--space-2);
}

.dropdown-menu.active {
    display: block;
    animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: var(--text-sm);
    background: transparent;
    border: none;
    cursor: pointer;
    width: 100%;
    text-align: left;
    transition: all var(--transition-fast);
}

.dropdown-item:hover {
    background: var(--bg-tertiary);
}

.dropdown-item i {
    color: var(--text-secondary);
    font-size: var(--text-base);
    width: 16px;
    text-align: center;
}

.dropdown-item.danger {
    color: var(--error);
}

.dropdown-item.danger i {
    color: var(--error);
}

.dropdown-item.danger:hover {
    background: rgba(255, 59, 48, 0.1);
}

.chat-history {
    flex: 1;
    padding: var(--space-4);
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0; /* 允许flex收缩 */
}

/* 聊天历史滚动条样式 */
.chat-history::-webkit-scrollbar {
    width: 6px;
}

.chat-history::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 3px;
}

.chat-history::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: 3px;
    transition: background var(--transition-fast);
}

.chat-history::-webkit-scrollbar-thumb:hover {
    background: var(--text-tertiary);
}

/* 聊天历史项目样式 */
.chat-history-item {
    position: relative;
    padding: var(--space-3) var(--space-4);
    margin-bottom: var(--space-2);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    border: 1px solid transparent;
}

.chat-history-item:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-light);
}

.chat-history-item:hover .chat-history-delete {
    opacity: 1;
}

.chat-history-item.active {
    background: var(--primary-blue-light);
    border-color: var(--primary-blue);
}

.chat-history-delete {
    position: absolute;
    top: var(--space-2);
    right: var(--space-2);
    width: 24px;
    height: 24px;
    background: var(--error);
    color: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xs);
    cursor: pointer;
    opacity: 0;
    transition: all var(--transition-fast);
}

.chat-history-delete:hover {
    background: #E60026;
    transform: scale(1.1);
}

.chat-history-title {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.chat-history-preview {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.chat-history-time {
    font-size: var(--text-xs);
    color: var(--text-tertiary);
    margin-top: var(--space-1);
}

.chat-history-empty {
    text-align: center;
    padding: var(--space-8) var(--space-4);
    color: var(--text-secondary);
}

.chat-history-empty .empty-icon {
    font-size: var(--text-2xl);
    color: var(--text-tertiary);
    margin-bottom: var(--space-3);
}

.chat-history-empty h4 {
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.chat-history-empty p {
    font-size: var(--text-xs);
    margin: 0;
}

.chat-main {
    display: flex;
    flex-direction: column;
    height: 100%; /* 确保占满容器高度 */
    overflow: hidden; /* 防止整体滚动 */
}

.chat-controls {
    display: flex;
    gap: var(--space-6);
    align-items: center;
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    flex-shrink: 0; /* 防止压缩 */
    min-height: 80px; /* 设置最小高度 */
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.control-group label {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    font-weight: var(--font-medium);
}

.control-select,
.control-input {
    padding: var(--space-2) var(--space-3);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    background: var(--bg-elevated);
    color: var(--text-primary);
    font-size: var(--text-sm);
    outline: none;
    transition: all var(--transition-fast);
}

.control-select:focus,
.control-input:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px var(--primary-blue-light);
}

.slider-container {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.control-slider {
    width: 100px;
    height: 4px;
    border-radius: 2px;
    background: var(--border-light);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.control-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-blue);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all var(--transition-fast);
}

.control-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
}

.slider-value {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    font-weight: var(--font-medium);
    min-width: 30px;
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    padding: var(--space-6);
    overflow-y: auto;
    overflow-x: hidden;
    background: var(--bg-secondary);
    background-image:
        radial-gradient(circle at 25% 25%, rgba(0, 122, 255, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(90, 200, 250, 0.02) 0%, transparent 50%);
    scroll-behavior: smooth;
    min-height: 0; /* 允许flex收缩 */
    max-height: calc(100vh - var(--navbar-height) - 300px); /* 限制最大高度 */
}

/* 聊天消息区域滚动条样式 */
.chat-messages::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: 4px;
    transition: background var(--transition-fast);
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--text-tertiary);
}

.welcome-message {
    text-align: center;
    padding: var(--space-12) var(--space-6);
    max-width: 500px;
    margin: 0 auto;
}

.welcome-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-blue), var(--info));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
    color: white;
    font-size: var(--text-2xl);
}

.welcome-message h3 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-4);
}

.welcome-message p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Chat Input */
.chat-input-area {
    padding: var(--space-6);
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border-top: 1px solid var(--border-light);
    flex-shrink: 0; /* 防止压缩 */
    min-height: 100px; /* 设置最小高度 */
}

.input-container {
    display: flex;
    gap: var(--space-3);
    align-items: flex-end;
    background: var(--bg-elevated);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--space-3);
    transition: all var(--transition-fast);
}

.input-container:focus-within {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px var(--primary-blue-light);
}

#chatInput {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    color: var(--text-primary);
    font-size: var(--text-base);
    font-family: var(--font-family);
    resize: none;
    min-height: 24px;
    max-height: 120px;
    line-height: 1.5;
}

#chatInput::placeholder {
    color: var(--text-tertiary);
}

.send-btn {
    width: 40px;
    height: 40px;
    background: var(--primary-blue);
    color: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--text-base);
}

.send-btn:hover {
    background: var(--primary-blue-hover);
    transform: scale(1.05);
}

.send-btn:active {
    transform: scale(0.95);
}

.send-btn:disabled {
    background: var(--border-medium);
    cursor: not-allowed;
    transform: none;
}

/* Database Section */
.database-form {
    background: var(--bg-elevated);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    border: 1px solid var(--border-light);
    margin-bottom: var(--space-8);
}

.form-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-6);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-5);
    margin-bottom: var(--space-6);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.form-group label {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
}

.form-input {
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--bg-elevated);
    color: var(--text-primary);
    font-size: var(--text-base);
    font-family: var(--font-family);
    outline: none;
    transition: all var(--transition-fast);
}

.form-input:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px var(--primary-blue-light);
}

.form-input::placeholder {
    color: var(--text-tertiary);
}

.form-actions {
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
}

.database-tables-section {
    background: var(--bg-elevated);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    padding: var(--space-8);
}

.database-tables {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--space-4);
}

/* Settings Section */
.settings-content {
    max-width: 800px;
}

.settings-group {
    background: var(--bg-elevated);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    padding: var(--space-8);
    margin-bottom: var(--space-8);
}

.settings-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-6);
}

.settings-grid {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
}

.setting-info h3 {
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.setting-info p {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0;
}

.setting-control {
    flex-shrink: 0;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 28px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--border-medium);
    transition: var(--transition-normal);
    border-radius: 28px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: var(--transition-normal);
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-blue);
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

.settings-actions {
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
    margin-top: var(--space-6);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-overlay);
    backdrop-filter: blur(4px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-overlay.active {
    display: flex;
}

.loading-content {
    background: var(--bg-elevated);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    text-align: center;
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-light);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--space-4);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--text-primary);
    font-weight: var(--font-medium);
    margin: 0;
}

/* Notification Panel */
.notification-panel {
    position: fixed;
    top: calc(var(--navbar-height) + var(--space-2));
    right: var(--space-6);
    width: 380px;
    max-height: 500px;
    background: var(--bg-elevated);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-light);
    z-index: 1600;
    transform: translateX(100%);
    opacity: 0;
    transition: all var(--transition-normal);
    overflow: hidden;
}

.notification-panel.active {
    transform: translateX(0);
    opacity: 1;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-5) var(--space-6);
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
}

.notification-header h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
}

.notification-actions {
    display: flex;
    gap: var(--space-2);
}

.notification-content {
    max-height: 400px;
    overflow-y: auto;
    padding: var(--space-4);
}

.notification-empty {
    text-align: center;
    padding: var(--space-8) var(--space-4);
    color: var(--text-secondary);
}

.notification-empty .empty-icon {
    font-size: var(--text-3xl);
    color: var(--text-tertiary);
    margin-bottom: var(--space-4);
}

.notification-empty h4 {
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.notification-empty p {
    font-size: var(--text-sm);
    margin: 0;
}

.notification-item {
    display: flex;
    gap: var(--space-3);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-3);
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.notification-item:hover {
    background: var(--bg-tertiary);
    transform: translateX(-2px);
}

.notification-item.unread {
    border-left: 4px solid var(--primary-blue);
    background: var(--primary-blue-light);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-base);
    flex-shrink: 0;
}

.notification-icon.success {
    background: var(--success);
    color: white;
}

.notification-icon.error {
    background: var(--error);
    color: white;
}

.notification-icon.warning {
    background: var(--warning);
    color: white;
}

.notification-icon.info {
    background: var(--info);
    color: white;
}

.notification-body {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.notification-message {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    line-height: 1.4;
    margin-bottom: var(--space-2);
}

.notification-time {
    font-size: var(--text-xs);
    color: var(--text-tertiary);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    font-size: var(--text-sm);
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    flex-shrink: 0;
}

.notification-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: calc(var(--navbar-height) + var(--space-6));
    right: var(--space-6);
    z-index: 1500;
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.toast {
    background: var(--bg-elevated);
    border-radius: var(--radius-lg);
    padding: var(--space-4) var(--space-5);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-light);
    min-width: 300px;
    display: flex;
    align-items: center;
    gap: var(--space-3);
    animation: slideIn 0.3s ease;
}

.toast.success {
    border-left: 4px solid var(--success);
}

.toast.error {
    border-left: 4px solid var(--error);
}

.toast.warning {
    border-left: 4px solid var(--warning);
}

.toast.info {
    border-left: 4px solid var(--info);
}

.toast-icon {
    font-size: var(--text-lg);
}

.toast.success .toast-icon {
    color: var(--success);
}

.toast.error .toast-icon {
    color: var(--error);
}

.toast.warning .toast-icon {
    color: var(--warning);
}

.toast.info .toast-icon {
    color: var(--info);
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-1) 0;
}

.toast-message {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    font-size: var(--text-base);
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.toast-close:hover {
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .chat-container {
        grid-template-columns: 1fr;
        height: calc(100vh - var(--navbar-height) - var(--space-8));
        max-height: none; /* 移动端不限制最大高度 */
    }

    .chat-sidebar {
        display: none;
    }

    .chat-messages {
        max-height: calc(100vh - var(--navbar-height) - 250px);
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 var(--space-4);
    }
    
    .nav-menu {
        display: none;
    }
    
    .content-section {
        padding: var(--space-4);
    }
    
    .chat-controls {
        flex-wrap: wrap;
        gap: var(--space-3);
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .documents-header {
        flex-direction: column;
        gap: var(--space-4);
        align-items: stretch;
    }
    
    .documents-actions {
        justify-content: space-between;
    }
    
    .search-box input {
        width: 150px;
    }
    
    .toast-container {
        left: var(--space-4);
        right: var(--space-4);
    }
    
    .toast {
        min-width: auto;
    }

    .notification-panel {
        left: var(--space-4);
        right: var(--space-4);
        width: auto;
        max-width: none;
    }

    .notification-panel.active {
        transform: translateX(0);
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-tertiary);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }

.mb-0 { margin-bottom: 0; }
.mb-2 { margin-bottom: var(--space-2); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }

.mt-0 { margin-top: 0; }
.mt-2 { margin-top: var(--space-2); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }

/* Document Cards */
.document-card {
    background: var(--bg-elevated);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-5);
    margin-bottom: var(--space-4);
    transition: all var(--transition-normal);
}

.document-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-blue);
}

.document-header {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-bottom: var(--space-4);
}

.document-icon {
    width: 48px;
    height: 48px;
    background: var(--primary-blue-light);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-blue);
    font-size: var(--text-lg);
}

.document-info {
    flex: 1;
}

.document-info h4 {
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-1) 0;
}

.document-meta {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.document-body {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.document-stats {
    display: flex;
    gap: var(--space-3);
}

.stat-badge {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-1) var(--space-2);
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    color: var(--text-secondary);
}

.document-actions {
    display: flex;
    gap: var(--space-2);
}

.btn-small {
    padding: var(--space-1) var(--space-3);
    font-size: var(--text-xs);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--space-12) var(--space-6);
    color: var(--text-secondary);
}

.empty-icon {
    font-size: var(--text-4xl);
    color: var(--text-tertiary);
    margin-bottom: var(--space-4);
}

.empty-state h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.empty-state p {
    margin: 0;
}

/* Chat Messages */
.message {
    display: flex;
    gap: var(--space-3);
    margin-bottom: var(--space-6);
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-base);
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: var(--primary-blue);
    color: white;
}

.assistant-message .message-avatar {
    background: var(--bg-elevated);
    color: var(--primary-blue);
    border: 1px solid var(--border-light);
}

.message-content {
    flex: 1;
    max-width: calc(100% - 52px);
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-2);
}

.message-sender {
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
}

.message-time {
    font-size: var(--text-xs);
    color: var(--text-tertiary);
}

.message-text {
    background: var(--bg-elevated);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    color: var(--text-primary);
    line-height: 1.6;
    word-wrap: break-word;
}

.user-message .message-text {
    background: var(--primary-blue);
    color: white;
    border-color: var(--primary-blue);
}

.message-sources {
    margin-top: var(--space-4);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    border: 1px solid var(--border-light);
}

.sources-header {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-3);
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    color: var(--text-secondary);
}

.sources-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.source-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-2);
    background: var(--bg-elevated);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
}

.source-index {
    width: 24px;
    height: 24px;
    background: var(--primary-blue);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xs);
    font-weight: var(--font-bold);
    flex-shrink: 0;
}

.source-info {
    flex: 1;
}

.source-name {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.source-similarity {
    font-size: var(--text-xs);
    color: var(--text-secondary);
}

/* Database Tables */
.table-card {
    background: var(--bg-elevated);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-5);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.table-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-blue);
}

.table-card.selected {
    border-color: var(--primary-blue);
    background: var(--primary-blue-light);
}

.table-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-blue-light);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-blue);
    font-size: var(--text-base);
}

.table-info h4 {
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-1) 0;
}

.table-info p {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0;
}

/* Settings Stats */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-4);
}

.stat-item {
    text-align: center;
    padding: var(--space-4);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--space-2);
}

.stat-value {
    font-size: var(--text-lg);
    font-weight: var(--font-bold);
    color: var(--text-primary);
}

.performance-info {
    text-align: center;
    padding: var(--space-8);
    color: var(--text-secondary);
}

/* Additional Button Styles */
.btn-large {
    padding: var(--space-4) var(--space-6);
    font-size: var(--text-base);
}

/* Enhanced Animations */
.stat-card,
.action-card,
.document-card,
.table-card {
    will-change: transform;
}

/* Focus States */
.btn:focus,
.form-input:focus,
.control-select:focus,
#chatInput:focus {
    outline: none;
    box-shadow: 0 0 0 3px var(--primary-blue-light);
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Improved Mobile Responsiveness */
@media (max-width: 640px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .actions-grid {
        grid-template-columns: 1fr;
    }

    .document-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-3);
    }

    .document-body {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-3);
    }

    .message {
        margin-bottom: var(--space-4);
    }

    .message-content {
        max-width: calc(100% - 44px);
    }

    .source-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-2);
    }
}
