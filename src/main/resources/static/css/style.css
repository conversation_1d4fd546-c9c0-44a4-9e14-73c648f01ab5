/* Apple Design System Variables */
:root {
    /* Colors - Apple Human Interface Guidelines */
    --primary-blue: #007AFF;
    --primary-blue-hover: #0056CC;
    --primary-blue-light: #E3F2FD;
    --secondary-gray: #8E8E93;
    --secondary-gray-light: #F2F2F7;

    /* Background Colors */
    --background-primary: #FFFFFF;
    --background-secondary: #F2F2F7;
    --background-tertiary: #FAFAFA;
    --background-elevated: #FFFFFF;
    --background-dark: #1C1C1E;
    --background-overlay: rgba(0, 0, 0, 0.4);

    /* Text Colors */
    --text-primary: #000000;
    --text-secondary: #3C3C43;
    --text-tertiary: #8E8E93;
    --text-quaternary: #C7C7CC;
    --text-white: #FFFFFF;
    --text-link: #007AFF;

    /* Border Colors */
    --border-color: #D1D1D6;
    --border-light: #E5E5EA;
    --border-medium: #C7C7CC;
    --border-focus: #007AFF;

    /* Status Colors */
    --success-green: #34C759;
    --success-green-light: #E8F5E8;
    --warning-orange: #FF9500;
    --warning-orange-light: #FFF4E6;
    --error-red: #FF3B30;
    --error-red-light: #FFEBEE;
    --info-blue: #5AC8FA;
    --info-blue-light: #E3F2FD;

    /* Typography */
    --font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
    --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

    /* Font Sizes */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-2xl: 24px;
    --font-size-3xl: 32px;
    --font-size-4xl: 48px;
    --font-size-5xl: 64px;

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Line Heights */
    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.6;

    /* Spacing System */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    --spacing-3xl: 64px;
    --spacing-4xl: 96px;

    /* Border Radius */
    --radius-xs: 4px;
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-2xl: 24px;
    --radius-full: 9999px;

    /* Shadows - Apple Style */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
    --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.2s ease;
    --transition-slow: 0.3s ease;

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;

    /* Layout */
    --container-max-width: 1200px;
    --sidebar-width: 280px;
    --header-height: 64px;
}

/* Reset and Base Styles */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background-color: var(--background-secondary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* Typography Base */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-md);
    line-height: var(--line-height-relaxed);
}

a {
    color: var(--text-link);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-blue-hover);
}

/* Container System */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.container-fluid {
    width: 100%;
    padding: 0 var(--spacing-lg);
}

/* Section Spacing */
.section {
    padding: var(--spacing-3xl) 0;
}

.section-sm {
    padding: var(--spacing-2xl) 0;
}

.section-lg {
    padding: var(--spacing-4xl) 0;
}

/* Navigation - Apple Style */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-light);
    z-index: var(--z-fixed);
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: var(--shadow-sm);
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-decoration: none;
    transition: opacity var(--transition-fast);
}

.nav-brand:hover {
    opacity: 0.8;
}

.nav-brand i {
    color: var(--primary-blue);
    font-size: var(--font-size-2xl);
}

.nav-menu {
    display: flex;
    gap: var(--spacing-xl);
    align-items: center;
}

.nav-link {
    position: relative;
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.nav-link:hover {
    color: var(--primary-blue);
    background-color: var(--primary-blue-light);
}

.nav-link.active {
    color: var(--primary-blue);
    background-color: var(--primary-blue-light);
}

/* Mobile Navigation */
.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
}

.nav-toggle span {
    width: 24px;
    height: 2px;
    background-color: var(--text-primary);
    transition: all var(--transition-fast);
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: var(--header-height);
        left: 0;
        right: 0;
        background: var(--background-elevated);
        flex-direction: column;
        padding: var(--spacing-lg);
        gap: var(--spacing-md);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
        box-shadow: var(--shadow-lg);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-link {
        width: 100%;
        text-align: center;
        padding: var(--spacing-md);
    }
}

/* Hero Section */
.hero {
    padding: 120px 0 var(--spacing-3xl);
    background: linear-gradient(135deg, #F2F2F7 0%, #FFFFFF 100%);
    display: flex;
    align-items: center;
    min-height: 100vh;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-lg);
    background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-blue) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.5;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    width: 100%;
}

.floating-card {
    position: absolute;
    background: var(--background-tertiary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    animation: float 3s ease-in-out infinite;
    transition: all 0.3s ease;
    /* 统一的卡片尺寸 */
    width: 100px;
    height: 100px;
    min-width: 100px;
    min-height: 100px;
    max-width: 100px;
    max-height: 100px;
}

.floating-card.clickable {
    cursor: pointer;
    z-index: 100;
    pointer-events: auto;
}

/* 右侧卡片的统一hover效果 */
.floating-card.clickable:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: var(--shadow-xl);
    background: var(--background-secondary);
}

/* 右侧卡片的统一active效果 */
.floating-card.clickable:active {
    transform: translateY(-2px) scale(1.02);
}

/* PDF卡片 - 右侧下方左 */
.floating-card:nth-child(1) {
    bottom: 20px !important;
    right: 140px !important;
    top: auto !important;
    left: auto !important;
    transform: none !important;
    animation: float 3s ease-in-out infinite;
    animation-delay: 0s;
}

/* Word卡片 - 右侧上方中央 */
.floating-card:nth-child(2) {
    top: 20px !important;
    right: 60px !important;
    bottom: auto !important;
    left: auto !important;
    transform: none !important;
    animation: float 3s ease-in-out infinite;
    animation-delay: 1s;
}

/* Text卡片 - 右侧下方右 */
.floating-card:nth-child(3) {
    bottom: 20px !important;
    right: 20px !important;
    top: auto !important;
    left: auto !important;
    transform: none !important;
    animation: float 3s ease-in-out infinite;
    animation-delay: 2s;
}

.floating-card i {
    font-size: var(--font-size-2xl);
    color: var(--primary-blue);
    margin-bottom: var(--spacing-xs);
}

.floating-card span {
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    text-align: center;
    line-height: 1.2;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Buttons - Apple Style */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: all var(--transition-fast);
    border: none;
    outline: none;
    position: relative;
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    font-family: var(--font-family);
}

.btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: transparent;
    border-radius: inherit;
    transition: background-color var(--transition-fast);
}

.btn:active::after {
    background-color: rgba(0, 0, 0, 0.1);
}

.btn i {
    font-size: 1.1em;
}

.btn-primary {
    background-color: var(--primary-blue);
    color: white;
    box-shadow: 0 2px 5px rgba(0, 122, 255, 0.2);
}

.btn-primary:hover {
    background-color: var(--primary-blue-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 122, 255, 0.3);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 122, 255, 0.2);
}

.btn-secondary {
    background-color: rgba(142, 142, 147, 0.12);
    color: var(--text-secondary);
    border: 1px solid transparent;
}

.btn-secondary:hover {
    background-color: rgba(142, 142, 147, 0.18);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:active {
    transform: translateY(0);
    background-color: rgba(142, 142, 147, 0.24);
}

/* Button Sizes */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
    height: 32px;
}

.btn-md {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
    height: 40px;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
    height: 48px;
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-blue);
    border: 1px solid var(--primary-blue);
}

.btn-outline:hover {
    background-color: var(--primary-blue-light);
    transform: translateY(-1px);
}

/* Card Components - Apple Style */
.card {
    background: var(--background-elevated);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    background: var(--background-tertiary);
}

.card-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.card-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: var(--spacing-xs) 0 0 0;
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    background: var(--background-tertiary);
}

.card-compact {
    padding: var(--spacing-md);
}

.card-compact .card-header,
.card-compact .card-body,
.card-compact .card-footer {
    padding: var(--spacing-md);
}

/* Form Components - Apple Style */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    background-color: var(--background-elevated);
    color: var(--text-primary);
    transition: all var(--transition-fast);
    outline: none;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    background-color: var(--background-primary);
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: var(--text-tertiary);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

.form-input-sm {
    padding: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.form-input-lg {
    padding: var(--spacing-lg);
    font-size: var(--font-size-lg);
}

.form-error {
    color: var(--error-red);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
}

.form-help {
    color: var(--text-tertiary);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
}

/* Toggle Switch - Apple Style */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 52px;
    height: 32px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--secondary-gray-light);
    transition: var(--transition-normal);
    border-radius: 16px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 28px;
    width: 28px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: var(--transition-normal);
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .toggle-slider {
    background-color: var(--primary-blue);
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

/* Slider Components - Apple Style */
.slider-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.slider-value {
    min-width: 40px;
    text-align: center;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    background: var(--background-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

/* Badge Components */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: var(--primary-blue-light);
    color: var(--primary-blue);
}

.badge-success {
    background: var(--success-green-light);
    color: var(--success-green);
}

.badge-warning {
    background: var(--warning-orange-light);
    color: var(--warning-orange);
}

.badge-error {
    background: var(--error-red-light);
    color: var(--error-red);
}

.badge-secondary {
    background: var(--secondary-gray-light);
    color: var(--text-secondary);
}

/* Alert Components */
.alert {
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    border: 1px solid;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.alert-icon {
    font-size: var(--font-size-lg);
    margin-top: 2px;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-xs);
}

.alert-message {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
}

.alert-info {
    background: var(--info-blue-light);
    border-color: var(--info-blue);
    color: var(--info-blue);
}

.alert-success {
    background: var(--success-green-light);
    border-color: var(--success-green);
    color: var(--success-green);
}

.alert-warning {
    background: var(--warning-orange-light);
    border-color: var(--warning-orange);
    color: var(--warning-orange);
}

.alert-error {
    background: var(--error-red-light);
    border-color: var(--error-red);
    color: var(--error-red);
}

/* Loading Spinner - Apple Style */
.spinner {
    width: 24px;
    height: 24px;
    border: 2px solid var(--border-light);
    border-top: 2px solid var(--primary-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-sm {
    width: 16px;
    height: 16px;
    border-width: 1px;
}

.spinner-lg {
    width: 32px;
    height: 32px;
    border-width: 3px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Sections */
.section {
    padding: var(--spacing-3xl) 0;
}

.section-dark {
    background: var(--background-secondary);
}

.container {
    max-width: 1400px; /* 增大容器最大宽度 */
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* 聊天部分的特殊容器 */
#chat .container {
    max-width: 1600px; /* 聊天部分更大的容器 */
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Upload Area - Apple Style */
.upload-card {
    max-width: 800px;
    margin: 0 auto;
}

.upload-area {
    background: var(--background-tertiary);
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-3xl);
    text-align: center;
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(0, 122, 255, 0.02) 50%, transparent 70%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.upload-area:hover::before,
.upload-area.dragover::before {
    opacity: 1;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--primary-blue);
    background: rgba(0, 122, 255, 0.05);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.upload-content h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    margin: var(--spacing-lg) 0 var(--spacing-sm);
    color: var(--text-primary);
}

.upload-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-base);
}

.upload-icon {
    font-size: var(--font-size-4xl);
    color: var(--primary-blue);
    margin-bottom: var(--spacing-md);
}

.upload-formats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin: var(--spacing-lg) 0;
    flex-wrap: wrap;
}

.format-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--background-secondary);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    border: 1px solid var(--border-light);
    transition: all var(--transition-fast);
}

.format-badge:hover {
    background: var(--primary-blue-light);
    color: var(--primary-blue);
    border-color: var(--primary-blue);
}

.format-badge i {
    font-size: var(--font-size-sm);
}

/* Progress Bar - Apple Style */
.upload-progress {
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--background-tertiary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
}

.progress-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.progress-title h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.progress-title p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

.progress-percentage {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-blue);
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: var(--background-secondary);
    border-radius: var(--radius-full);
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-blue), var(--success-green));
    border-radius: var(--radius-full);
    transition: width 0.3s ease;
    width: 0%;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    text-align: center;
    margin-top: var(--spacing-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
}

/* Result Card */
.upload-result {
    margin-top: var(--spacing-xl);
}

.result-card {
    background: var(--background-tertiary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    transition: all var(--transition-normal);
}

.result-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.result-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.result-icon {
    font-size: var(--font-size-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--success-green-light);
}

.result-icon.success {
    color: var(--success-green);
}

.result-icon.error {
    color: var(--error-red);
    background: var(--error-red-light);
}

.result-header h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin: 0;
    color: var(--text-primary);
}

.result-body {
    padding: var(--spacing-lg);
}

.result-body p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-base);
}

.result-actions {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    justify-content: center;
}

.result-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.detail-item {
    background: var(--background-secondary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
    transition: all var(--transition-fast);
}

.detail-item:hover {
    background: var(--background-primary);
    box-shadow: var(--shadow-sm);
    transform: translateY(-2px);
}

.detail-label {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-xs);
}

.detail-value {
    font-size: var(--font-size-base);
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
}

/* Chat Interface - Apple Style */
.chat-interface,
.chat-container {
    background: white !important;
    border-radius: var(--radius-lg) !important;
    box-shadow: var(--shadow-lg) !important;
    overflow: hidden !important;
    max-width: 1200px !important;
    width: 100% !important;
    margin: 0 auto !important;
    border: 1px solid var(--border-light) !important;
    display: flex !important;
    flex-direction: column !important;
    height: 70vh !important;
    min-height: 600px !important;
    max-height: 800px !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    transition: all var(--transition-normal) !important;
}

.chat-interface:hover,
.chat-container:hover {
    box-shadow: var(--shadow-xl) !important;
    transform: translateY(-2px) !important;
}

/* Chat Header */
.chat-header {
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.chat-status {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--success-green);
}

.chat-messages {
    flex: 1; /* 占据剩余空间 */
    overflow-y: auto;
    padding: var(--spacing-xl);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    background: var(--background-primary);
    scroll-behavior: smooth;
    /* 自定义滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

.message {
    display: flex;
    gap: var(--spacing-md);
    max-width: 85%; /* 增大消息最大宽度 */
    align-items: flex-start;
    animation: fadeInUp 0.4s ease;
    margin-bottom: var(--spacing-md);
}

.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.bot-message {
    align-self: flex-start;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.bot-message .message-avatar {
    background: var(--background-secondary);
    color: var(--text-secondary);
    border: 2px solid var(--border-color);
}

.message-content {
    background: var(--background-secondary);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-radius: var(--radius-xl);
    position: relative;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.2s ease;
}

.message-content:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

/* 用户消息样式 - 黑色文字，白色背景 */
.user-message .message-content {
    background: white !important;
    color: #000000 !important; /* 确保黑色文字 */
    border: 2px solid #007AFF !important;
    box-shadow: 0 4px 20px rgba(0, 122, 255, 0.15) !important;
    font-weight: 500 !important;
    line-height: 1.5 !important;
}

.user-message .message-content:hover {
    box-shadow: 0 6px 25px rgba(0, 122, 255, 0.25) !important;
    border-color: #0056CC !important;
    background: #fafafa !important;
}

/* 确保用户消息中的文字颜色 */
.user-message .message-text,
.user-message .message-text *,
.user-message .message-content,
.user-message .message-content * {
    color: #000000 !important; /* 强制黑色文字 */
    -webkit-text-fill-color: #000000 !important; /* Safari兼容 */
    font-size: 16px !important;
}

.user-message .message-time {
    color: #666666 !important;
    font-size: 12px !important;
    margin-top: 8px !important;
}

/* 机器人消息样式 - 浅灰背景，深色文字 */
.bot-message .message-content {
    background: #f8f9fa !important;
    color: #333333 !important;
    border: 1px solid #e5e5e7 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.bot-message .message-text {
    color: #333333 !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
}

.bot-message .message-time {
    color: #666666 !important;
    font-size: 12px !important;
    margin-top: 8px !important;
}

.message-content p {
    margin: 0;
    line-height: 1.5;
}

.message-time {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    margin-top: var(--spacing-xs);
}

.retrieved-chunks {
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.chunk-item {
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.chunk-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-xs);
    opacity: 0.8;
}

.fallback-notice {
    margin-top: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(255, 149, 0, 0.1);
    border: 1px solid var(--warning-orange);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.fallback-notice i {
    color: var(--warning-orange);
}

.fallback-notice span {
    color: var(--text-secondary);
}

/* Query Controls Panel - 查询控制面板 */
.query-controls-panel {
    padding: 20px !important;
    background: #f8f9fa !important;
    border-top: 1px solid #e5e5e7 !important;
    border-bottom: 1px solid #e5e5e7 !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
}

.control-row {
    display: flex;
    gap: var(--spacing-xl);
    align-items: flex-start;
    flex-wrap: wrap;
}

.control-group {
    flex: 1;
    min-width: 200px;
}

.control-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.control-label i {
    color: var(--primary-color);
}

.value-display {
    color: var(--primary-color);
    font-weight: 700;
    background: rgba(0, 122, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: var(--font-size-xs);
}

/* Similarity Slider Styles */
.similarity-control {
    flex: 2;
}

.slider-container {
    position: relative;
    margin-bottom: var(--spacing-xs);
}

.similarity-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--border-color);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.similarity-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 122, 255, 0.3);
    transition: all 0.2s ease;
}

.similarity-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
}

.similarity-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 122, 255, 0.3);
    transition: all 0.2s ease;
}

.similarity-slider::-moz-range-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
}

.similarity-slider:focus {
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.2);
}

.slider-track-labels {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-xs);
    padding: 0 10px;
}

.track-label {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-weight: 500;
}

.slider-description {
    margin-top: var(--spacing-xs);
}

.slider-description small {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    font-style: italic;
}

.similarity-hint {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(0, 122, 255, 0.05);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--primary-color);
    transition: all 0.3s ease;
}

.similarity-hint i {
    color: var(--primary-color);
    font-size: var(--font-size-xs);
}

.similarity-hint span {
    font-size: var(--font-size-xs);
    color: var(--text-primary);
    font-weight: 500;
}

/* Option Select Styles */
.option-select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--background-primary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all 0.2s ease;
}

.option-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.option-select:hover {
    border-color: var(--primary-color);
}

/* Chat Input */
.chat-input-container {
    padding: var(--spacing-xl);
    background: var(--background-primary);
    border-top: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* 聊天输入区域样式 */
.chat-input-wrapper {
    display: flex !important;
    gap: var(--spacing-md) !important;
    align-items: flex-end !important;
    width: 100% !important;
}

#chatInput,
.chat-input {
    flex: 1 !important;
    padding: var(--spacing-lg) !important;
    border: 2px solid #e5e5e7 !important;
    border-radius: 26px !important; /* 圆角输入框 */
    font-size: 16px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    background: white !important;
    resize: none !important;
    min-height: 52px !important;
    max-height: 150px !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    line-height: 1.5 !important;
    color: #000000 !important; /* 强制黑色文字 */
    -webkit-text-fill-color: #000000 !important; /* Safari兼容 */
}

#chatInput:focus,
.chat-input:focus {
    outline: none !important;
    border-color: #007AFF !important;
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1), 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    background: white !important;
}

#chatInput::placeholder,
.chat-input::placeholder {
    color: #999999 !important;
    opacity: 1 !important;
}

.chat-input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.chat-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.option-select {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    background: var(--background-tertiary);
    color: var(--text-secondary);
}

/* 发送按钮样式 - 确保优先级 */
#sendButton,
.send-button {
    width: 52px !important;
    height: 52px !important;
    border: none !important;
    border-radius: 50% !important;
    background: #007AFF !important; /* 直接使用颜色值确保显示 */
    color: white !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3) !important;
    font-size: 18px !important;
    flex-shrink: 0 !important;
}

#sendButton:hover,
.send-button:hover {
    background: #0056CC !important;
    transform: scale(1.05) translateY(-1px) !important;
    box-shadow: 0 6px 20px rgba(0, 122, 255, 0.4) !important;
}

#sendButton:active,
.send-button:active {
    transform: scale(0.95) !important;
}

#sendButton:disabled,
.send-button:disabled {
    background: #999999 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
    opacity: 0.6 !important;
    transform: none;
}

/* Documents Grid */
.documents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.document-card {
    background: var(--background-tertiary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
    border: 1px solid var(--border-color);
}

.document-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.document-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.document-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    background: var(--primary-blue);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
}

.document-info h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.document-meta {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.document-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin: var(--spacing-md) 0;
}

.stat-item {
    text-align: center;
    padding: var(--spacing-sm);
    background: var(--background-secondary);
    border-radius: var(--radius-sm);
}

.stat-value {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-blue);
}

.stat-label {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    margin-top: var(--spacing-xs);
}

.document-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.btn-small {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    border-radius: var(--radius-sm);
}

.btn-danger {
    background: var(--error-red);
    color: white;
}

.btn-danger:hover {
    background: #D70015;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    background: var(--background-tertiary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-xl);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--background-secondary);
    border-top: 3px solid var(--primary-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 100px;
    right: var(--spacing-lg);
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--background-tertiary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--primary-blue);
    min-width: 300px;
    animation: slideIn 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.toast.success {
    border-left-color: var(--success-green);
}

.toast.error {
    border-left-color: var(--error-red);
}

.toast.warning {
    border-left-color: var(--warning-orange);
}

.toast-icon {
    font-size: var(--font-size-lg);
}

.toast.success .toast-icon {
    color: var(--success-green);
}

.toast.error .toast-icon {
    color: var(--error-red);
}

.toast.warning .toast-icon {
    color: var(--warning-orange);
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.toast-message {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
}

.toast-close:hover {
    background: var(--background-secondary);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design - Apple Style */

/* Large Desktop */
@media (min-width: 1441px) {
    .container {
        max-width: 1400px;
    }

    .hero-title {
        font-size: var(--font-size-5xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-2xl);
    }
}

/* Tablet and Small Desktop */
@media (max-width: 1024px) {
    .container {
        max-width: 100%;
        padding: 0 var(--spacing-lg);
    }

    .hero-content {
        gap: var(--spacing-2xl);
    }

    .chat-options {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }

    .option-group {
        flex: 1;
        min-width: 200px;
    }

    .documents-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

/* Mobile Landscape and Tablet Portrait */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-xl);
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-buttons {
        justify-content: center;
    }

    .hero-visual {
        order: -1;
        min-height: 250px;
    }

    /* 右侧卡片 - 平板设备统一样式 */
    .floating-card {
        transform: scale(0.8) !important;
    }

    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .chat-input-wrapper {
        flex-direction: column;
        align-items: stretch;
    }

    .chat-options {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    .send-button {
        align-self: flex-end;
    }

    .documents-grid {
        grid-template-columns: 1fr;
    }

    .toast-container {
        left: var(--spacing-md);
        right: var(--spacing-md);
    }

    .toast {
        min-width: auto;
    }

    .upload-formats {
        flex-wrap: wrap;
    }
}

/* Mobile Portrait */
@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .section {
        padding: var(--spacing-xl) 0;
    }

    .section-header {
        margin-bottom: var(--spacing-xl);
    }

    .section-title {
        font-size: var(--font-size-2xl);
    }

    .section-subtitle {
        font-size: var(--font-size-base);
    }

    .upload-area {
        padding: var(--spacing-xl);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    /* 在小屏幕上进一步缩小悬浮卡片 */
    .floating-card {
        transform: scale(0.6) !important;
    }

    .chat-options {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .option-group {
        width: 100%;
    }

    .chat-option-slider {
        width: 100%;
    }

    .result-header,
    .result-body,
    .result-actions {
        padding: var(--spacing-md);
    }

    .result-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--background-secondary);
    border-radius: var(--radius-sm);
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--radius-sm);
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--text-tertiary);
}

/* Focus States */
.btn:focus,
.chat-input:focus,
.option-select:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.2);
}

/* Animations */
.document-card,
.message,
.upload-area {
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Embedding Warning Styles */
.embedding-warning {
    position: fixed;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--background-tertiary);
    border: 1px solid var(--warning-orange);
    border-left: 4px solid var(--warning-orange);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 9999;
    max-width: 600px;
    width: 90%;
    opacity: 1;
    transition: all 0.3s ease;
}

.warning-content {
    padding: var(--spacing-lg);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.warning-content i {
    color: var(--warning-orange);
    font-size: var(--font-size-xl);
    margin-top: var(--spacing-xs);
}

.warning-text {
    flex: 1;
}

.warning-text strong {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.warning-text p {
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
}

.warning-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.warning-actions .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.warning-actions .btn-primary {
    background: var(--primary-color);
    color: white;
}

.warning-actions .btn-primary:hover {
    background: #0056CC;
    transform: translateY(-1px);
}

.warning-actions .btn-secondary {
    background: var(--text-secondary);
    color: white;
}

.warning-actions .btn-secondary:hover {
    background: #666;
    transform: translateY(-1px);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Upload Progress Styles */
.upload-progress .progress-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.upload-progress .fa-spinner {
    color: var(--primary-color);
    font-size: var(--font-size-xl);
}

.upload-progress .progress-text strong {
    display: block;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.upload-progress .progress-text p {
    color: var(--text-secondary);
    margin: 0;
    font-size: var(--font-size-sm);
}

/* Drag and Drop Styles */
.upload-area.drag-over {
    border-color: var(--primary-color);
    background: rgba(0, 122, 255, 0.05);
    transform: scale(1.02);
}

/* Typing Indicator Styles */
.typing-indicator .typing-dots {
    display: flex;
    gap: 4px;
    padding: var(--spacing-sm);
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--text-secondary);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Retrieved Chunks Styles */
.retrieved-chunks {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--background-tertiary);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--primary-color);
}

.retrieved-chunks h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.chunk {
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: var(--background-primary);
    border-radius: var(--radius-xs);
    border: 1px solid var(--border-color);
}

.chunk:last-child {
    margin-bottom: 0;
}

.chunk-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-xs);
}

.chunk-source {
    color: var(--primary-color);
    font-weight: 600;
}

.chunk-similarity {
    color: var(--text-secondary);
    background: rgba(0, 122, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

.chunk-content {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chat-container {
        max-width: 100%;
        height: 80vh;
        min-height: 500px;
        margin: var(--spacing-sm);
        border-radius: var(--radius-lg);
    }

    .chat-messages {
        padding: var(--spacing-lg);
        gap: var(--spacing-md);
    }

    .message {
        max-width: 95%;
    }

    .message-content {
        padding: var(--spacing-md) var(--spacing-lg);
    }

    .chat-input-container {
        padding: var(--spacing-lg);
    }

    .chat-input {
        min-height: 48px;
        padding: var(--spacing-md);
    }

    .send-button {
        width: 48px;
        height: 48px;
    }

    .query-controls-panel {
        padding: var(--spacing-sm) var(--spacing-lg);
    }

    .control-row {
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .control-group {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .chat-container {
        height: 85vh;
        margin: var(--spacing-xs);
        border-radius: var(--radius-md);
    }

    .chat-messages {
        padding: var(--spacing-md);
    }

    .message-content {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .chat-input {
        font-size: var(--font-size-sm);
    }
}

/* 强制修复用户输入和消息的文字颜色问题 */
input#chatInput,
textarea#chatInput,
.chat-input,
input.chat-input,
textarea.chat-input {
    color: #000000 !important;
    -webkit-text-fill-color: #000000 !important;
    -moz-text-fill-color: #000000 !important;
}

/* 强制修复用户消息的文字颜色 */
.message.user-message,
.message.user-message *,
.user-message,
.user-message *,
div.user-message,
div.user-message * {
    color: #000000 !important;
    -webkit-text-fill-color: #000000 !important;
    -moz-text-fill-color: #000000 !important;
}

/* 确保没有其他样式覆盖用户文字颜色 */
.chat-container .user-message,
.chat-container .user-message *,
.chat-messages .user-message,
.chat-messages .user-message *,
#chatMessages .user-message,
#chatMessages .user-message * {
    color: #000000 !important;
    -webkit-text-fill-color: #000000 !important;
    -moz-text-fill-color: #000000 !important;
}

/* 文档管理样式 */
.documents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
}

.document-card {
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.document-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.document-header {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.document-icon {
    width: 48px;
    height: 48px;
    background: var(--background-tertiary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    flex-shrink: 0;
}

.document-info {
    flex: 1;
    min-width: 0;
}

.document-name {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
    word-break: break-word;
}

.document-meta {
    display: flex;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.document-actions {
    display: flex;
    gap: var(--spacing-xs);
    flex-shrink: 0;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-sm);
    background: var(--background-tertiary);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.action-btn.delete-btn:hover {
    background: #ff3b30;
}

.document-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    font-size: var(--font-size-xs);
}

.document-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.status-success {
    background: rgba(52, 199, 89, 0.1);
    color: #34c759;
}

.status-processing {
    background: rgba(255, 149, 0, 0.1);
    color: #ff9500;
}

.document-time {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-secondary);
}

/* 空状态和错误状态 */
.empty-state,
.error-state {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--text-secondary);
    grid-column: 1 / -1;
}

.empty-icon,
.error-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.empty-state h3,
.error-state h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.empty-state p,
.error-state p {
    margin-bottom: var(--spacing-lg);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .documents-grid {
        grid-template-columns: 1fr;
        padding: var(--spacing-md);
    }

    .document-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .document-actions {
        align-self: flex-end;
        margin-top: var(--spacing-sm);
    }
}
