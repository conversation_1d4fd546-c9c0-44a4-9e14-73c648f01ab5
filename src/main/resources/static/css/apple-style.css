/* Apple Design System - AI Cousin Pro */

/* CSS Variables */
:root {
    /* Colors - Apple Human Interface Guidelines */
    --primary-blue: #007AFF;
    --primary-blue-hover: #0056CC;
    --primary-blue-light: rgba(0, 122, 255, 0.1);
    
    --secondary-gray: #8E8E93;
    --secondary-gray-light: #F2F2F7;
    
    /* Background Colors */
    --bg-primary: #FFFFFF;
    --bg-secondary: #F9F9F9;
    --bg-tertiary: #FAFAFA;
    --bg-elevated: #FFFFFF;
    --bg-overlay: rgba(0, 0, 0, 0.4);
    --bg-glass: rgba(255, 255, 255, 0.8);
    
    /* Text Colors */
    --text-primary: #1D1D1F;
    --text-secondary: #86868B;
    --text-tertiary: #A1A1A6;
    --text-white: #FFFFFF;
    --text-link: #007AFF;
    
    /* Border Colors */
    --border-light: #E5E5E7;
    --border-medium: #D2D2D7;
    --border-focus: #007AFF;
    
    /* Status Colors */
    --success: #30D158;
    --success-light: rgba(48, 209, 88, 0.1);
    --warning: #FF9F0A;
    --warning-light: rgba(255, 159, 10, 0.1);
    --error: #FF3B30;
    --error-light: rgba(255, 59, 48, 0.1);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
    --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', monospace;
    
    /* Font Sizes */
    --text-xs: 12px;
    --text-sm: 14px;
    --text-base: 16px;
    --text-lg: 18px;
    --text-xl: 20px;
    --text-2xl: 24px;
    --text-3xl: 32px;
    --text-4xl: 48px;
    --text-5xl: 64px;
    
    /* Font Weights */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    
    /* Spacing */
    --space-1: 4px;
    --space-2: 8px;
    --space-3: 12px;
    --space-4: 16px;
    --space-5: 20px;
    --space-6: 24px;
    --space-8: 32px;
    --space-10: 40px;
    --space-12: 48px;
    --space-16: 64px;
    --space-20: 80px;
    --space-24: 96px;
    
    /* Border Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-2xl: 24px;
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.2s ease;
    --transition-slow: 0.3s ease;
    
    /* Layout */
    --container-max: 1200px;
    --header-height: 72px;
}

/* Reset */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
}

body {
    font-family: var(--font-family);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-semibold);
    line-height: 1.2;
    margin-bottom: var(--space-4);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
    margin-bottom: var(--space-4);
    line-height: 1.6;
}

a {
    color: var(--text-link);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-blue-hover);
}

/* Container */
.container {
    max-width: var(--container-max);
    margin: 0 auto;
    padding: 0 var(--space-6);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-light);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.nav-container {
    max-width: var(--container-max);
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    text-decoration: none;
}

.brand-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--primary-blue), #5AC8FA);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--text-base);
}

.nav-menu {
    display: flex;
    gap: var(--space-8);
    align-items: center;
}

.nav-link {
    position: relative;
    color: var(--text-secondary);
    font-weight: var(--font-medium);
    font-size: var(--text-sm);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-blue);
    background-color: var(--primary-blue-light);
}

.nav-actions {
    display: flex;
    gap: var(--space-3);
    align-items: center;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-5);
    border-radius: var(--radius-full);
    font-weight: var(--font-medium);
    font-size: var(--text-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    border: none;
    outline: none;
    text-decoration: none;
    white-space: nowrap;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.btn-primary {
    background-color: var(--primary-blue);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
}

.btn-primary:hover {
    background-color: var(--primary-blue-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.btn-secondary {
    background-color: var(--bg-elevated);
    color: var(--text-primary);
    border: 1px solid var(--border-light);
}

.btn-secondary:hover {
    background-color: var(--bg-secondary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-blue);
    border: 1px solid var(--primary-blue);
}

.btn-outline:hover {
    background-color: var(--primary-blue-light);
    transform: translateY(-1px);
}

.btn-large {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
}

/* Hero Section */
.hero {
    padding: calc(var(--header-height) + var(--space-20)) 0 var(--space-24);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(0, 122, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.hero-container {
    max-width: var(--container-max);
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;
    min-height: 600px;
}

.hero-content {
    z-index: 2;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: var(--primary-blue-light);
    color: var(--primary-blue);
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    margin-bottom: var(--space-6);
}

.badge-icon {
    font-size: var(--text-base);
}

.hero-title {
    font-size: var(--text-5xl);
    font-weight: var(--font-bold);
    line-height: 1.1;
    margin-bottom: var(--space-6);
    color: var(--text-primary);
}

.gradient-text {
    background: linear-gradient(135deg, var(--primary-blue), #5AC8FA);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--text-xl);
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--space-8);
    max-width: 500px;
}

.hero-actions {
    display: flex;
    gap: var(--space-4);
    margin-bottom: var(--space-12);
}

.hero-stats {
    display: flex;
    gap: var(--space-8);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

/* Hero Visual */
.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 500px;
}

.visual-container {
    position: relative;
    width: 400px;
    height: 400px;
}

.central-orb {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
}

.orb-inner {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-blue), #5AC8FA);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--text-2xl);
    box-shadow: var(--shadow-xl);
    animation: float 3s ease-in-out infinite;
}

.orb-rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ring {
    position: absolute;
    border: 2px solid var(--primary-blue);
    border-radius: 50%;
    opacity: 0.3;
    animation: pulse 2s ease-in-out infinite;
}

.ring-1 {
    width: 160px;
    height: 160px;
    top: -80px;
    left: -80px;
    animation-delay: 0s;
}

.ring-2 {
    width: 200px;
    height: 200px;
    top: -100px;
    left: -100px;
    animation-delay: 0.5s;
}

.ring-3 {
    width: 240px;
    height: 240px;
    top: -120px;
    left: -120px;
    animation-delay: 1s;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-card {
    position: absolute;
    background: var(--bg-elevated);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    box-shadow: var(--shadow-lg);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
    width: 80px;
    height: 80px;
    border: 1px solid var(--border-light);
    transition: all var(--transition-normal);
}

.floating-card i {
    font-size: var(--text-xl);
    color: var(--primary-blue);
}

.floating-card span {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    font-weight: var(--font-medium);
    text-align: center;
}

.document-card {
    top: 20px;
    right: 60px;
    animation: float 3s ease-in-out infinite;
    animation-delay: 0s;
}

.chat-card {
    bottom: 40px;
    left: 40px;
    animation: float 3s ease-in-out infinite;
    animation-delay: 1s;
}

.brain-card {
    bottom: 40px;
    right: 40px;
    animation: float 3s ease-in-out infinite;
    animation-delay: 2s;
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes pulse {
    0%, 100% { 
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.3;
    }
    50% { 
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.1;
    }
}

/* Features Section */
.features {
    padding: var(--space-24) 0;
    background: var(--bg-primary);
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-16);
}

.section-title {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-4);
}

.section-subtitle {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-8);
}

.feature-card {
    background: var(--bg-elevated);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    border: 1px solid var(--border-light);
    transition: all var(--transition-normal);
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-blue);
}

.feature-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, var(--primary-blue), #5AC8FA);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
    color: white;
    font-size: var(--text-xl);
}

.feature-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-3);
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Workspace Section */
.workspace {
    padding: var(--space-24) 0;
    background: var(--bg-secondary);
}

.workspace-header {
    text-align: center;
    margin-bottom: var(--space-12);
}

.workspace-title {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-8);
}

.workspace-tabs {
    display: inline-flex;
    background: var(--bg-elevated);
    border-radius: var(--radius-full);
    padding: var(--space-1);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-full);
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-weight: var(--font-medium);
    font-size: var(--text-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.tab-btn.active {
    background: var(--primary-blue);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
}

.tab-btn:hover:not(.active) {
    background: var(--primary-blue-light);
    color: var(--primary-blue);
}

.tab-content {
    display: none;
    background: var(--bg-elevated);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.tab-content.active {
    display: block;
}

/* Chat Workspace */
.chat-workspace {
    display: grid;
    grid-template-columns: 300px 1fr;
    height: 700px;
}

.chat-sidebar {
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-light);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
}

.chat-history {
    flex: 1;
    padding: var(--space-4);
    overflow-y: auto;
}

.chat-main {
    display: flex;
    flex-direction: column;
}

.chat-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-info h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-1) 0;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success);
}

.status-indicator.online .status-dot {
    background: var(--success);
    animation: pulse-dot 2s ease-in-out infinite;
}

@keyframes pulse-dot {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.chat-controls {
    display: flex;
    gap: var(--space-6);
    align-items: center;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.control-group label {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    font-weight: var(--font-medium);
}

.control-select,
.control-input {
    padding: var(--space-2) var(--space-3);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    background: var(--bg-elevated);
    color: var(--text-primary);
    font-size: var(--text-sm);
    outline: none;
    transition: all var(--transition-fast);
}

.control-select:focus,
.control-input:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px var(--primary-blue-light);
}

.control-slider {
    width: 100px;
    height: 4px;
    border-radius: 2px;
    background: var(--border-light);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.control-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-blue);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all var(--transition-fast);
}

.control-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    padding: var(--space-6);
    overflow-y: auto;
    background: var(--bg-secondary);
    background-image:
        radial-gradient(circle at 25% 25%, rgba(0, 122, 255, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(90, 200, 250, 0.02) 0%, transparent 50%);
}

.welcome-message {
    text-align: center;
    padding: var(--space-12) var(--space-6);
    max-width: 500px;
    margin: 0 auto;
}

.welcome-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-blue), #5AC8FA);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
    color: white;
    font-size: var(--text-2xl);
}

.welcome-message h3 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-4);
}

.welcome-message p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Chat Input */
.chat-input-area {
    padding: var(--space-6);
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border-top: 1px solid var(--border-light);
}

.input-container {
    display: flex;
    gap: var(--space-3);
    align-items: flex-end;
    background: var(--bg-elevated);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--space-3);
    transition: all var(--transition-fast);
}

.input-container:focus-within {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px var(--primary-blue-light);
}

#chatInput {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    color: var(--text-primary);
    font-size: var(--text-base);
    font-family: var(--font-family);
    resize: none;
    min-height: 24px;
    max-height: 120px;
    line-height: 1.5;
}

#chatInput::placeholder {
    color: var(--text-tertiary);
}

.send-btn {
    width: 40px;
    height: 40px;
    background: var(--primary-blue);
    color: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--text-base);
}

.send-btn:hover {
    background: var(--primary-blue-hover);
    transform: scale(1.05);
}

.send-btn:active {
    transform: scale(0.95);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--space-12);
    }

    .hero-visual {
        order: -1;
        height: 300px;
    }

    .visual-container {
        width: 300px;
        height: 300px;
    }

    .hero-title {
        font-size: var(--text-4xl);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .nav-menu {
        display: none;
    }

    .chat-workspace {
        grid-template-columns: 1fr;
        height: auto;
    }

    .chat-sidebar {
        display: none;
    }

    .chat-controls {
        flex-wrap: wrap;
        gap: var(--space-3);
    }
}

/* Documents Workspace */
.documents-workspace {
    padding: var(--space-8);
}

.upload-area {
    background: var(--bg-secondary);
    border: 2px dashed var(--border-medium);
    border-radius: var(--radius-xl);
    padding: var(--space-12);
    text-align: center;
    transition: all var(--transition-normal);
    cursor: pointer;
    margin-bottom: var(--space-8);
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(0, 122, 255, 0.02) 50%, transparent 70%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.upload-area:hover::before,
.upload-area.dragover::before {
    opacity: 1;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--primary-blue);
    background: var(--primary-blue-light);
    transform: translateY(-2px);
}

.upload-content h3 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: var(--space-4) 0 var(--space-2);
}

.upload-content p {
    color: var(--text-secondary);
    margin-bottom: var(--space-6);
}

.upload-icon {
    font-size: var(--text-4xl);
    color: var(--primary-blue);
    margin-bottom: var(--space-4);
}

.documents-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--space-6);
}

.document-card {
    background: var(--bg-elevated);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.document-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-blue);
}

.document-header {
    padding: var(--space-5);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.document-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-blue), #5AC8FA);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--text-base);
}

.document-info h4 {
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-1) 0;
}

.document-meta {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.document-body {
    padding: var(--space-5);
}

.document-stats {
    display: flex;
    gap: var(--space-4);
    margin-bottom: var(--space-4);
}

.stat-badge {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-1) var(--space-2);
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    color: var(--text-secondary);
}

.document-actions {
    display: flex;
    gap: var(--space-2);
}

.btn-small {
    padding: var(--space-1) var(--space-3);
    font-size: var(--text-xs);
    border-radius: var(--radius-sm);
}

/* Database Workspace */
.database-workspace {
    padding: var(--space-8);
}

.database-form {
    background: var(--bg-elevated);
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    border: 1px solid var(--border-light);
    margin-bottom: var(--space-8);
}

.database-form h3 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-6);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-5);
    margin-bottom: var(--space-6);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.form-group label {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
}

.form-input {
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--bg-elevated);
    color: var(--text-primary);
    font-size: var(--text-base);
    font-family: var(--font-family);
    outline: none;
    transition: all var(--transition-fast);
}

.form-input:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px var(--primary-blue-light);
}

.form-input::placeholder {
    color: var(--text-tertiary);
}

.form-actions {
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
}

.database-tables {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--space-4);
}

.table-card {
    background: var(--bg-elevated);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    padding: var(--space-5);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.table-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-blue);
}

.table-card.selected {
    border-color: var(--primary-blue);
    background: var(--primary-blue-light);
}

.table-name {
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.table-info {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}



/* Status Bar */
.status-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--border-light);
    padding: var(--space-3) var(--space-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--text-sm);
    z-index: 100;
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.status-label {
    color: var(--text-secondary);
}

.status-value {
    color: var(--text-primary);
    font-weight: var(--font-medium);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-overlay);
    backdrop-filter: blur(4px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-overlay.active {
    display: flex;
}

.loading-content {
    background: var(--bg-elevated);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    text-align: center;
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-light);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--space-4);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--text-primary);
    font-weight: var(--font-medium);
    margin: 0;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--space-6);
    right: var(--space-6);
    z-index: 1500;
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.toast {
    background: var(--bg-elevated);
    border-radius: var(--radius-lg);
    padding: var(--space-4) var(--space-5);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-light);
    min-width: 300px;
    display: flex;
    align-items: center;
    gap: var(--space-3);
    animation: slideIn 0.3s ease;
}

.toast.success {
    border-left: 4px solid var(--success);
}

.toast.error {
    border-left: 4px solid var(--error);
}

.toast.warning {
    border-left: 4px solid var(--warning);
}

.toast-icon {
    font-size: var(--text-lg);
}

.toast.success .toast-icon {
    color: var(--success);
}

.toast.error .toast-icon {
    color: var(--error);
}

.toast.warning .toast-icon {
    color: var(--warning);
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-1) 0;
}

.toast-message {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    font-size: var(--text-base);
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.toast-close:hover {
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Chat Messages */
.message {
    display: flex;
    gap: var(--space-3);
    margin-bottom: var(--space-6);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-sm);
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: var(--primary-blue);
    color: white;
}

.assistant-message .message-avatar {
    background: var(--bg-elevated);
    color: var(--primary-blue);
    border: 1px solid var(--border-light);
}

.message-content {
    flex: 1;
    max-width: calc(100% - 50px);
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-2);
}

.message-sender {
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
}

.message-time {
    font-size: var(--text-xs);
    color: var(--text-tertiary);
}

.message-text {
    background: var(--bg-elevated);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    color: var(--text-primary);
    line-height: 1.6;
    word-wrap: break-word;
}

.user-message .message-text {
    background: var(--primary-blue);
    color: white;
    border-color: var(--primary-blue);
}

.message-sources {
    margin-top: var(--space-4);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.sources-header {
    padding: var(--space-3) var(--space-4);
    background: var(--bg-elevated);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-secondary);
}

.sources-list {
    padding: var(--space-2);
}

.source-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-sm);
    transition: background-color var(--transition-fast);
}

.source-item:hover {
    background: var(--bg-elevated);
}

.source-index {
    width: 20px;
    height: 20px;
    background: var(--primary-blue);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    flex-shrink: 0;
}

.source-info {
    flex: 1;
}

.source-name {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.source-similarity {
    font-size: var(--text-xs);
    color: var(--text-secondary);
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: var(--space-12) var(--space-6);
    color: var(--text-secondary);
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: var(--bg-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
    font-size: var(--text-2xl);
    color: var(--text-tertiary);
}

.empty-state h3 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-3);
}

.empty-state p {
    color: var(--text-secondary);
    margin: 0;
}

/* Status Classes */
.status-success {
    color: var(--success) !important;
}

.status-warning {
    color: var(--warning) !important;
}

.status-error {
    color: var(--error) !important;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }

.hidden { display: none; }
.visible { display: block; }

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-tertiary);
}

/* Additional Mobile Responsive */
@media (max-width: 480px) {
    .container {
        padding: 0 var(--space-4);
    }

    .hero {
        padding: calc(var(--header-height) + var(--space-12)) 0 var(--space-16);
    }

    .hero-title {
        font-size: var(--text-3xl);
    }

    .hero-subtitle {
        font-size: var(--text-base);
    }

    .hero-actions {
        flex-direction: column;
        width: 100%;
    }

    .btn-large {
        width: 100%;
        justify-content: center;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .workspace-tabs {
        flex-direction: column;
        width: 100%;
    }

    .tab-btn {
        width: 100%;
        justify-content: center;
    }

    .chat-workspace {
        grid-template-columns: 1fr;
        height: 600px;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .documents-list {
        grid-template-columns: 1fr;
    }

    .database-tables {
        grid-template-columns: 1fr;
    }

    .toast-container {
        left: var(--space-4);
        right: var(--space-4);
    }

    .toast {
        min-width: auto;
    }
}
