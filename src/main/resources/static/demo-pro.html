<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Cousin Pro - 演示页面</title>
    <link rel="stylesheet" href="css/pro-style.css?v=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Demo specific styles */
        .demo-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(90deg, #007AFF, #5AC8FA);
            color: white;
            text-align: center;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 500;
            z-index: 1001;
            animation: slideDown 0.5s ease;
        }
        
        @keyframes slideDown {
            from { transform: translateY(-100%); }
            to { transform: translateY(0); }
        }
        
        .demo-banner a {
            color: white;
            text-decoration: underline;
            margin-left: 8px;
        }
        
        /* Adjust navbar for demo banner */
        .navbar {
            top: 48px;
        }
        
        .main-content {
            margin-top: calc(var(--navbar-height) + 48px);
        }
        
        /* Demo features showcase */
        .demo-features {
            background: linear-gradient(135deg, #007AFF, #5AC8FA);
            color: white;
            padding: var(--space-20) 0;
            text-align: center;
        }
        
        .demo-features h2 {
            font-size: var(--text-4xl);
            font-weight: var(--font-bold);
            margin-bottom: var(--space-6);
        }
        
        .demo-features p {
            font-size: var(--text-lg);
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto var(--space-12);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-8);
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-6);
        }
        
        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: var(--radius-xl);
            padding: var(--space-8);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all var(--transition-normal);
        }
        
        .demo-card:hover {
            transform: translateY(-4px);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .demo-card-icon {
            font-size: var(--text-3xl);
            margin-bottom: var(--space-4);
        }
        
        .demo-card h3 {
            font-size: var(--text-xl);
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-3);
        }
        
        .demo-card p {
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .demo-actions {
            margin-top: var(--space-12);
        }
        
        .demo-btn {
            background: white;
            color: var(--primary-blue);
            padding: var(--space-4) var(--space-8);
            border-radius: var(--radius-full);
            text-decoration: none;
            font-weight: var(--font-semibold);
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            transition: all var(--transition-normal);
            margin: 0 var(--space-2);
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        
        .demo-btn.secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }
        
        .demo-btn.secondary:hover {
            background: white;
            color: var(--primary-blue);
        }
    </style>
</head>
<body>
    <!-- Demo Banner -->
    <div class="demo-banner">
        🎉 AI Cousin Pro - 全新Apple风格设计演示
        <a href="index.html">返回原版本</a>
        <a href="index-pro.html">完整版本</a>
    </div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <div class="brand-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <span class="brand-text">AI Cousin Pro</span>
            </div>
            <div class="nav-menu">
                <a href="#features" class="nav-link">功能特性</a>
                <a href="#demo" class="nav-link">在线演示</a>
                <a href="https://github.com/ai-cousin" class="nav-link">GitHub</a>
            </div>
            <div class="nav-actions">
                <button class="btn btn-primary" onclick="window.open('index-pro.html', '_blank')">
                    <i class="fas fa-rocket"></i>
                    立即体验
                </button>
            </div>
        </div>
    </nav>

    <!-- Demo Features Section -->
    <section class="demo-features" id="features">
        <div class="container">
            <h2>现代化的AI文档助手</h2>
            <p>基于Apple设计语言重新打造，提供企业级的智能文档问答体验。简约、优雅、功能强大。</p>
            
            <div class="demo-grid">
                <div class="demo-card">
                    <div class="demo-card-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>Apple设计语言</h3>
                    <p>遵循Apple Human Interface Guidelines，提供一致、直观的用户体验。每个细节都经过精心设计。</p>
                </div>
                
                <div class="demo-card">
                    <div class="demo-card-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3>高性能架构</h3>
                    <p>三层向量化策略、智能缓存、异步处理，确保系统在高并发场景下依然流畅运行。</p>
                </div>
                
                <div class="demo-card">
                    <div class="demo-card-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3>智能问答系统</h3>
                    <p>基于RAG技术，结合文档内容和数据库信息，提供精准、有引用来源的智能回答。</p>
                </div>
                
                <div class="demo-card">
                    <div class="demo-card-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3>多数据源集成</h3>
                    <p>支持PDF、DOCX、TXT、MD文档，以及MySQL、PostgreSQL数据库的统一检索和问答。</p>
                </div>
                
                <div class="demo-card">
                    <div class="demo-card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>实时监控</h3>
                    <p>完整的系统监控面板，实时查看服务状态、性能指标、缓存统计等关键信息。</p>
                </div>
                
                <div class="demo-card">
                    <div class="demo-card-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>响应式设计</h3>
                    <p>完美适配桌面、平板、手机等各种设备，随时随地享受一致的使用体验。</p>
                </div>
            </div>
            
            <div class="demo-actions">
                <a href="index-pro.html" class="demo-btn">
                    <i class="fas fa-play"></i>
                    立即体验
                </a>
                <a href="#demo" class="demo-btn secondary">
                    <i class="fas fa-eye"></i>
                    查看演示
                </a>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Dashboard Section -->
        <section id="demo" class="content-section active">
            <div class="section-header">
                <h1 class="section-title">功能演示</h1>
                <p class="section-subtitle">体验AI Cousin Pro的核心功能</p>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">156</div>
                        <div class="stat-label">已处理文档</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">2,847</div>
                        <div class="stat-label">智能问答</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">3</div>
                        <div class="stat-label">连接数据库</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">94.2%</div>
                        <div class="stat-label">缓存命中率</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h2 class="section-subtitle">核心功能</h2>
                <div class="actions-grid">
                    <div class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-upload"></i>
                        </div>
                        <div class="action-content">
                            <h3>智能文档上传</h3>
                            <p>支持拖拽上传，自动解析PDF、DOCX、TXT、MD格式文档</p>
                        </div>
                    </div>
                    <div class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="action-content">
                            <h3>智能问答对话</h3>
                            <p>基于文档内容的精准问答，提供详细的引用来源</p>
                        </div>
                    </div>
                    <div class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-plug"></i>
                        </div>
                        <div class="action-content">
                            <h3>数据库集成</h3>
                            <p>连接MySQL、PostgreSQL，理解表结构，提供数据问答</p>
                        </div>
                    </div>
                    <div class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="action-content">
                            <h3>系统管理</h3>
                            <p>实时监控、性能调优、缓存管理等企业级功能</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="system-status">
                <h2 class="section-subtitle">系统状态</h2>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-header">
                            <i class="fas fa-server"></i>
                            <span>服务状态</span>
                        </div>
                        <div class="status-value">
                            <span class="status-indicator online"></span>
                            <span class="status-text">运行正常</span>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-header">
                            <i class="fas fa-vector-square"></i>
                            <span>向量化服务</span>
                        </div>
                        <div class="status-value">
                            <span class="status-indicator online"></span>
                            <span class="status-text">MILVUS_FALLBACK_ONLY</span>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-header">
                            <i class="fas fa-database"></i>
                            <span>Milvus数据库</span>
                        </div>
                        <div class="status-value">
                            <span class="status-indicator online"></span>
                            <span class="status-text">连接正常</span>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-header">
                            <i class="fas fa-memory"></i>
                            <span>缓存系统</span>
                        </div>
                        <div class="status-value">
                            <span class="status-indicator online"></span>
                            <span class="status-text">运行正常</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Demo Chat Interface -->
            <div class="demo-chat-section">
                <h2 class="section-subtitle">智能问答演示</h2>
                <div class="chat-container" style="height: 500px;">
                    <div class="chat-main">
                        <div class="chat-controls">
                            <div class="control-group">
                                <label>数据源</label>
                                <select class="control-select">
                                    <option value="all">全部</option>
                                    <option value="documents">文档</option>
                                    <option value="database">数据库</option>
                                </select>
                            </div>
                            <div class="control-group">
                                <label>检索数量</label>
                                <select class="control-select">
                                    <option value="5" selected>5</option>
                                    <option value="10">10</option>
                                </select>
                            </div>
                            <div class="control-group">
                                <label>相似度阈值</label>
                                <div class="slider-container">
                                    <input type="range" class="control-slider" min="0" max="1" step="0.1" value="0.3">
                                    <span class="slider-value">0.3</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="chat-messages">
                            <div class="message assistant-message">
                                <div class="message-avatar">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <div class="message-content">
                                    <div class="message-header">
                                        <span class="message-sender">AI助手</span>
                                        <span class="message-time">14:01</span>
                                    </div>
                                    <div class="message-text">
                                        您好！我是AI Cousin Pro智能助手。我可以帮您分析文档内容、回答问题、查询数据库信息。请告诉我您想了解什么？
                                    </div>
                                </div>
                            </div>
                            
                            <div class="message user-message">
                                <div class="message-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="message-content">
                                    <div class="message-header">
                                        <span class="message-sender">您</span>
                                        <span class="message-time">14:02</span>
                                    </div>
                                    <div class="message-text">
                                        这个系统有哪些主要功能？
                                    </div>
                                </div>
                            </div>
                            
                            <div class="message assistant-message">
                                <div class="message-avatar">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <div class="message-content">
                                    <div class="message-header">
                                        <span class="message-sender">AI助手</span>
                                        <span class="message-time">14:02</span>
                                    </div>
                                    <div class="message-text">
                                        AI Cousin Pro 主要包含以下核心功能：<br><br>
                                        <strong>1. 智能文档管理</strong><br>
                                        • 支持PDF、DOCX、TXT、MD格式文档上传<br>
                                        • 自动文档解析和向量化处理<br>
                                        • 智能文档分块和索引<br><br>
                                        
                                        <strong>2. 智能问答系统</strong><br>
                                        • 基于RAG技术的精准问答<br>
                                        • 提供详细的引用来源<br>
                                        • 支持上下文理解<br><br>
                                        
                                        <strong>3. 数据库集成</strong><br>
                                        • 支持MySQL、PostgreSQL连接<br>
                                        • 自动理解数据库表结构<br>
                                        • 提供数据相关的智能问答<br><br>
                                        
                                        <strong>4. 系统监控</strong><br>
                                        • 实时性能监控<br>
                                        • 缓存统计和管理<br>
                                        • 服务状态检查
                                    </div>
                                    <div class="message-sources">
                                        <div class="sources-header">
                                            <i class="fas fa-link"></i>
                                            <span>参考来源</span>
                                        </div>
                                        <div class="sources-list">
                                            <div class="source-item">
                                                <span class="source-index">1</span>
                                                <div class="source-info">
                                                    <div class="source-name">系统功能文档.pdf</div>
                                                    <div class="source-similarity">相似度: 95.2%</div>
                                                </div>
                                            </div>
                                            <div class="source-item">
                                                <span class="source-index">2</span>
                                                <div class="source-info">
                                                    <div class="source-name">技术架构说明.md</div>
                                                    <div class="source-similarity">相似度: 87.6%</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="chat-input-area">
                            <div class="input-container">
                                <textarea placeholder="输入您的问题..." rows="1" disabled style="opacity: 0.6;"></textarea>
                                <button class="send-btn" disabled style="opacity: 0.6;">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                            <p style="text-align: center; color: var(--text-secondary); font-size: var(--text-sm); margin-top: var(--space-2);">
                                这是演示界面，请访问 <a href="index-pro.html" style="color: var(--primary-blue);">完整版本</a> 体验真实功能
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

    <script>
        // Demo page JavaScript
        document.addEventListener('DOMContentLoaded', () => {
            // Show welcome toast
            setTimeout(() => {
                showToast('success', '欢迎体验', 'AI Cousin Pro - 现代化的Apple风格设计！');
            }, 1000);

            // Animate stats
            setTimeout(() => {
                animateNumbers();
            }, 2000);
        });

        function animateNumbers() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach((element, index) => {
                const text = element.textContent;
                if (text.includes('%')) {
                    const number = parseFloat(text);
                    animateNumber(element, 0, number, '%');
                } else if (text.includes(',')) {
                    const number = parseInt(text.replace(',', ''));
                    animateNumberWithComma(element, 0, number);
                } else if (!isNaN(parseInt(text))) {
                    const number = parseInt(text);
                    animateNumber(element, 0, number);
                }
            });
        }

        function animateNumber(element, start, end, suffix = '') {
            const duration = 2000;
            const startTime = Date.now();
            
            const updateNumber = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const currentNumber = Math.floor(start + (end - start) * progress);
                
                element.textContent = currentNumber + suffix;
                
                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                }
            };
            
            updateNumber();
        }

        function animateNumberWithComma(element, start, end) {
            const duration = 2000;
            const startTime = Date.now();
            
            const updateNumber = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const currentNumber = Math.floor(start + (end - start) * progress);
                
                element.textContent = currentNumber.toLocaleString();
                
                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                }
            };
            
            updateNumber();
        }

        function showToast(type, title, message) {
            const container = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            
            const iconMap = {
                success: 'check-circle',
                error: 'exclamation-circle',
                warning: 'exclamation-triangle',
                info: 'info-circle'
            };
            
            toast.innerHTML = `
                <div class="toast-icon">
                    <i class="fas fa-${iconMap[type]}"></i>
                </div>
                <div class="toast-content">
                    <div class="toast-title">${title}</div>
                    <div class="toast-message">${message}</div>
                </div>
                <button class="toast-close">
                    <i class="fas fa-times"></i>
                </button>
            `;
            
            toast.querySelector('.toast-close').addEventListener('click', () => {
                toast.remove();
            });
            
            container.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 5000);
        }

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
</body>
</html>
