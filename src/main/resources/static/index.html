<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Cousin Pro - 智能文档问答系统</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #007AFF, #5AC8FA);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .container {
            text-align: center;
            max-width: 600px;
            padding: 40px 20px;
        }

        .logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .logo i {
            font-size: 48px;
            color: white;
        }

        h1 {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 16px;
            letter-spacing: -0.02em;
        }

        .subtitle {
            font-size: 20px;
            font-weight: 400;
            opacity: 0.9;
            margin-bottom: 40px;
            line-height: 1.5;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 30px;
        }

        .spinner {
            width: 24px;
            height: 24px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 16px;
            font-weight: 500;
        }

        .manual-links {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .manual-links p {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 20px;
        }

        .links {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .version-info {
            margin-top: 40px;
            font-size: 12px;
            opacity: 0.7;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            h1 {
                font-size: 36px;
            }
            
            .subtitle {
                font-size: 18px;
            }
            
            .links {
                flex-direction: column;
                align-items: center;
            }
            
            .link {
                width: 200px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <i class="fas fa-brain"></i>
        </div>
        
        <h1>AI Cousin Pro</h1>
        <p class="subtitle">现代化的智能文档问答系统<br>基于Apple设计语言重新打造</p>
        
        <div class="loading">
            <div class="spinner"></div>
            <span class="loading-text">正在加载应用...</span>
        </div>
        
        <div class="manual-links">
            <p>如果页面没有自动跳转，请手动选择：</p>
            <div class="links">
                <a href="/index-pro.html" class="link">
                    <i class="fas fa-rocket"></i>
                    完整应用
                </a>
                <a href="/demo-pro.html" class="link">
                    <i class="fas fa-eye"></i>
                    功能演示
                </a>
            </div>
        </div>
        
        <div class="version-info">
            AI Cousin Pro v2.0 - Apple Style Design
        </div>
    </div>

    <script>
        // 页面加载完成后自动跳转
        document.addEventListener('DOMContentLoaded', function() {
            // 显示加载动画2秒后跳转
            setTimeout(function() {
                window.location.href = '/index-pro.html';
            }, 2000);
        });

        // 如果用户点击了任何地方，立即跳转
        document.addEventListener('click', function() {
            window.location.href = '/index-pro.html';
        });

        // 按任意键也跳转
        document.addEventListener('keydown', function() {
            window.location.href = '/index-pro.html';
        });
    </script>
</body>
</html>
