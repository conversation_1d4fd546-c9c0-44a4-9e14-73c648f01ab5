<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档管理 - AI Cousin</title>
    <link rel="stylesheet" href="css/style.css?v=2.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: #f5f5f7;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .page-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .page-header {
            background: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007AFF;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .search-box {
            flex: 1;
            max-width: 300px;
            padding: 10px 15px;
            border: 2px solid #e5e5e7;
            border-radius: 8px;
            font-size: 14px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.2s ease;
        }
        .btn-primary {
            background: #007AFF;
            color: white;
        }
        .btn-primary:hover {
            background: #0056CC;
        }
        .btn-danger {
            background: #ff3b30;
            color: white;
        }
        .btn-danger:hover {
            background: #d70015;
        }
        .documents-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .documents-header {
            padding: 20px;
            border-bottom: 1px solid #e5e5e7;
            font-weight: 600;
            color: #333;
        }
        .loading-state, .error-state, .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        .loading-spinner, .error-icon, .empty-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #007AFF;
        }
        .error-icon {
            color: #ff3b30;
        }
        .loading-spinner i {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>📚 文档管理中心</h1>
            <p>管理您上传的所有文档，查看处理状态和统计信息</p>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalDocs">0</div>
                <div class="stat-label">总文档数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalSize">0 MB</div>
                <div class="stat-label">总大小</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalChunks">0</div>
                <div class="stat-label">总片段数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="processedDocs">0</div>
                <div class="stat-label">已处理</div>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
            <input type="text" class="search-box" placeholder="搜索文档..." id="searchBox">
            <div>
                <button class="btn btn-primary" onclick="refreshDocuments()">
                    <i class="fas fa-sync"></i> 刷新
                </button>
                <button class="btn btn-primary" onclick="uploadDocument()">
                    <i class="fas fa-upload"></i> 上传文档
                </button>
                <button class="btn btn-danger" onclick="deleteSelected()">
                    <i class="fas fa-trash"></i> 删除选中
                </button>
            </div>
        </div>

        <!-- 文档列表 -->
        <div class="documents-container">
            <div class="documents-header">
                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()"> 
                文档列表
            </div>
            <div class="documents-grid" id="documentsGrid">
                <!-- 文档将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量存储文档数据
        let documents = [];
        let documentStats = {};

        // API基础URL
        const API_BASE = '/api/rag';

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', () => {
            loadDocumentsFromAPI();
            setupSearch();
        });

        /**
         * 从API加载文档数据
         */
        async function loadDocumentsFromAPI() {
            try {
                console.log('📂 从API加载文档数据...');
                showLoading(true);

                const response = await fetch(`${API_BASE}/documents`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('📚 获取到文档数据:', data);

                // 更新全局变量
                documents = data.documents || [];
                documentStats = data.stats || {};

                // 处理文档数据，添加文件类型
                documents = documents.map(doc => ({
                    ...doc,
                    type: getFileTypeFromName(doc.name)
                }));

                // 更新页面显示
                displayDocuments();
                updateStats();

                showLoading(false);

            } catch (error) {
                console.error('❌ 加载文档数据失败:', error);
                showLoading(false);
                showError('加载文档数据失败: ' + error.message);

                // 显示空状态
                documents = [];
                displayDocuments();
                updateStats();
            }
        }

        /**
         * 根据文件名获取文件类型
         */
        function getFileTypeFromName(fileName) {
            const extension = fileName.split('.').pop().toLowerCase();
            switch (extension) {
                case 'pdf': return 'pdf';
                case 'doc':
                case 'docx': return 'docx';
                case 'txt': return 'txt';
                case 'md': return 'md';
                case 'ppt':
                case 'pptx': return 'pptx';
                case 'xls':
                case 'xlsx': return 'xlsx';
                default: return 'file';
            }
        }

        /**
         * 显示/隐藏加载状态
         */
        function showLoading(show) {
            const grid = document.getElementById('documentsGrid');
            if (show) {
                grid.innerHTML = `
                    <div class="loading-state">
                        <div class="loading-spinner">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                        <h3>加载中...</h3>
                        <p>正在获取文档列表</p>
                    </div>
                `;
            }
        }

        /**
         * 显示错误信息
         */
        function showError(message) {
            const grid = document.getElementById('documentsGrid');
            grid.innerHTML = `
                <div class="error-state">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3>加载失败</h3>
                    <p>${message}</p>
                    <button class="btn btn-primary" onclick="loadDocumentsFromAPI()">
                        <i class="fas fa-retry"></i> 重试
                    </button>
                </div>
            `;
        }

        function displayDocuments(docs = documents) {
            const grid = document.getElementById('documentsGrid');
            
            if (docs.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-folder-open"></i>
                        </div>
                        <h3>暂无文档</h3>
                        <p>您还没有上传任何文档。</p>
                        <button class="btn btn-primary" onclick="uploadDocument()">
                            <i class="fas fa-upload"></i> 立即上传
                        </button>
                    </div>
                `;
                return;
            }

            const documentsHTML = docs.map(doc => `
                <div class="document-card" data-doc-id="${doc.id}">
                    <div class="document-header">
                        <input type="checkbox" class="doc-checkbox" value="${doc.id}">
                        <div class="document-icon">
                            <i class="fas ${getFileIcon(doc.type)}"></i>
                        </div>
                        <div class="document-info">
                            <h4 class="document-name">${doc.name}</h4>
                            <div class="document-meta">
                                <span class="document-size">${doc.size}</span>
                                <span class="document-chunks">${doc.chunks} 片段</span>
                            </div>
                        </div>
                        <div class="document-actions">
                            <button class="action-btn" onclick="downloadDocument('${doc.id}')" title="下载">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="action-btn delete-btn" onclick="deleteDocument('${doc.id}')" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="document-footer">
                        <div class="document-status ${doc.status === '已处理' ? 'status-success' : 'status-processing'}">
                            <i class="fas ${doc.status === '已处理' ? 'fa-check-circle' : 'fa-spinner fa-spin'}"></i>
                            ${doc.status}
                        </div>
                        <div class="document-time">
                            <i class="fas fa-clock"></i>
                            ${doc.uploadTime}
                        </div>
                    </div>
                </div>
            `).join('');

            grid.innerHTML = documentsHTML;
        }

        function getFileIcon(type) {
            switch (type) {
                case 'pdf': return 'fa-file-pdf';
                case 'docx':
                case 'doc': return 'fa-file-word';
                case 'txt': return 'fa-file-text';
                case 'md': return 'fa-file-code';
                default: return 'fa-file';
            }
        }

        function updateStats() {
            // 使用后端返回的统计数据，如果没有则计算本地数据
            if (documentStats && Object.keys(documentStats).length > 0) {
                document.getElementById('totalDocs').textContent = documentStats.total || documents.length;
                document.getElementById('totalSize').textContent = documentStats.totalSize || '0 B';
                document.getElementById('totalChunks').textContent = documentStats.totalChunks || 0;

                // 计算已处理文档数
                const statusCount = documentStats.statusCount || {};
                const processedDocs = statusCount['已处理'] || 0;
                document.getElementById('processedDocs').textContent = processedDocs;
            } else {
                // 如果没有后端统计数据，则计算本地数据
                document.getElementById('totalDocs').textContent = documents.length;

                const totalSizeMB = documents.reduce((sum, doc) => {
                    const size = parseFloat(doc.size.replace(/[^\d.]/g, ''));
                    return sum + (doc.size.includes('KB') ? size / 1024 : size);
                }, 0);
                document.getElementById('totalSize').textContent = totalSizeMB.toFixed(1) + ' MB';

                const totalChunks = documents.reduce((sum, doc) => sum + (doc.chunks || 0), 0);
                document.getElementById('totalChunks').textContent = totalChunks;

                const processedDocs = documents.filter(doc => doc.status === '已处理').length;
                document.getElementById('processedDocs').textContent = processedDocs;
            }
        }

        function setupSearch() {
            const searchBox = document.getElementById('searchBox');
            searchBox.addEventListener('input', (e) => {
                const query = e.target.value.toLowerCase();
                const filtered = documents.filter(doc => 
                    doc.name.toLowerCase().includes(query) ||
                    doc.status.toLowerCase().includes(query)
                );
                displayDocuments(filtered);
            });
        }

        function refreshDocuments() {
            console.log('🔄 刷新文档列表');
            loadDocumentsFromAPI();
            showToast('info', '刷新中', '正在重新加载文档列表...');
        }

        function uploadDocument() {
            alert('上传功能：请返回主页面使用文档上传功能');
            window.location.href = '/';
        }

        function downloadDocument(docId) {
            const doc = documents.find(d => d.id === docId);
            if (doc) {
                showToast('info', '下载中', `正在下载 ${doc.name}`);
                // 这里可以实现实际的下载逻辑
            }
        }

        async function deleteDocument(docId) {
            if (!confirm('确定要删除这个文档吗？删除后无法恢复。')) {
                return;
            }

            try {
                console.log('🗑️ 删除文档:', docId);
                showToast('info', '删除中', '正在删除文档...');

                const response = await fetch(`${API_BASE}/documents/${docId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('✅ 文档删除响应:', result);

                if (result.status === 'SUCCESS') {
                    showToast('success', '删除成功', '文档已成功删除');
                    // 重新加载文档列表
                    loadDocumentsFromAPI();
                } else if (result.status === 'NOT_FOUND') {
                    showToast('warning', '文档不存在', '要删除的文档不存在');
                } else {
                    showToast('error', '删除失败', result.message || '未知错误');
                }

            } catch (error) {
                console.error('❌ 删除文档失败:', error);
                showToast('error', '删除失败', error.message);
            }
        }

        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.doc-checkbox');
            checkboxes.forEach(cb => cb.checked = selectAll.checked);
        }

        async function deleteSelected() {
            const selected = Array.from(document.querySelectorAll('.doc-checkbox:checked'))
                .map(cb => cb.value);

            if (selected.length === 0) {
                showToast('warning', '未选择', '请先选择要删除的文档');
                return;
            }

            if (!confirm(`确定要删除选中的 ${selected.length} 个文档吗？`)) {
                return;
            }

            try {
                showToast('info', '批量删除中', `正在删除 ${selected.length} 个文档...`);

                // 并行删除所有选中的文档
                const deletePromises = selected.map(async (docId) => {
                    const response = await fetch(`${API_BASE}/documents/${docId}`, {
                        method: 'DELETE'
                    });

                    if (!response.ok) {
                        throw new Error(`删除文档 ${docId} 失败: HTTP ${response.status}`);
                    }

                    return response.json();
                });

                const results = await Promise.all(deletePromises);
                console.log('✅ 批量删除结果:', results);

                // 检查删除结果
                const successCount = results.filter(r => r.status === 'SUCCESS').length;
                const failCount = selected.length - successCount;

                if (failCount === 0) {
                    showToast('success', '删除成功', `已成功删除 ${successCount} 个文档`);
                } else {
                    showToast('warning', '部分删除失败', `成功删除 ${successCount} 个，失败 ${failCount} 个`);
                }

                // 重新加载文档列表
                loadDocumentsFromAPI();
                document.getElementById('selectAll').checked = false;

            } catch (error) {
                console.error('❌ 批量删除失败:', error);
                showToast('error', '删除失败', error.message);
            }
        }

        function showToast(type, title, message) {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `<strong>${title}</strong><br>${message}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'error' ? '#ff3b30' : type === 'warning' ? '#ff9500' : type === 'info' ? '#007AFF' : '#34c759'};
                color: white;
                padding: 15px;
                border-radius: 8px;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }
    </script>
</body>
</html>
