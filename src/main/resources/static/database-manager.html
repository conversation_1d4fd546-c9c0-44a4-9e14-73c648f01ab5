<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Cousin - 数据库管理</title>
    <link rel="stylesheet" href="css/style.css?v=2.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Add some specific styles for this page if needed */
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .form-group input[type="text"],
        .form-group input[type="password"],
        .form-group input[type="number"],
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            box-sizing: border-box;
            font-size: 16px;
            background-color: #f9f9f9;
            color: #333;
        }
        .table-list {
            margin-top: 30px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            padding: 25px;
        }
        .table-list h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: 600;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
        }
        .table-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
            font-size: 16px;
            color: #555;
        }
        .table-item:last-child {
            border-bottom: none;
        }
        .table-item .btn {
            padding: 8px 15px;
            font-size: 14px;
        }
        .status-message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
            display: none; /* Hidden by default */
        }
        .status-message.success {
            background-color: #e6ffe6;
            color: #008000;
            border: 1px solid #008000;
        }
        .status-message.error {
            background-color: #ffe6e6;
            color: #cc0000;
            border: 1px solid #cc0000;
        }
        .status-message.info {
            background-color: #e6f7ff;
            color: #007AFF;
            border: 1px solid #007AFF;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-brain"></i>
                <span>AI Cousin</span>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">智能问答</a>
                <a href="document-manager.html" class="nav-link">文档管理</a>
                <a href="database-manager.html" class="nav-link active">数据库管理</a>
            </div>
        </div>
    </nav>

    <!-- Database Management Section -->
    <section id="database-management" class="section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">数据库管理</h2>
                <p class="section-subtitle">配置数据库连接，采集表Schema并存储到向量库</p>
            </div>
            
            <div class="card">
                <h3 class="card-title">数据库连接配置</h3>
                <div class="form-group">
                    <label for="dbType">数据库类型:</label>
                    <select id="dbType" name="dbType">
                        <option value="mysql">MySQL</option>
                        <option value="postgresql">PostgreSQL</option>
                        <option value="sqlserver">SQL Server</option>
                        <option value="oracle">Oracle</option>
                        <!-- Add more types as needed -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="dbHost">主机:</label>
                    <input type="text" id="dbHost" name="dbHost" value="localhost" placeholder="e.g., localhost or *************">
                </div>
                <div class="form-group">
                    <label for="dbPort">端口:</label>
                    <input type="number" id="dbPort" name="dbPort" value="3306" placeholder="e.g., 3306 for MySQL">
                </div>
                <div class="form-group">
                    <label for="dbName">数据库名:</label>
                    <input type="text" id="dbName" name="dbName" placeholder="e.g., mydatabase">
                </div>
                <div class="form-group">
                    <label for="dbUsername">用户名:</label>
                    <input type="text" id="dbUsername" name="dbUsername" placeholder="e.g., root">
                </div>
                <div class="form-group">
                    <label for="dbPassword">密码:</label>
                    <input type="password" id="dbPassword" name="dbPassword" placeholder="Password">
                </div>
                <button class="btn btn-primary" id="testConnectionBtn">
                    <i class="fas fa-database"></i>
                    测试连接并列出表
                </button>
                <div id="connectionStatus" class="status-message"></div>
            </div>

            <div class="table-list" id="availableTablesSection" style="display: none;">
                <h3>可用表列表</h3>
                <div id="tablesContainer">
                    <!-- Tables will be loaded here -->
                </div>
                <div id="collectionStatus" class="status-message"></div>
            </div>
        </div>
    </section>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>处理中...</p>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <script src="js/database-manager.js"></script>
    <script src="js/app.js"></script> <!-- Keep app.js for common functions like showToast -->
</body>
</html> 