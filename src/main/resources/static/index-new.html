<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Cousin - 智能文档助手</title>
    <link rel="stylesheet" href="css/apple-style.css?v=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <div class="brand-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <span class="brand-text">AI Cousin</span>
            </div>
            <div class="nav-menu">
                <a href="#home" class="nav-link active">首页</a>
                <a href="#features" class="nav-link">功能</a>
                <a href="#workspace" class="nav-link">工作台</a>
                <a href="#contact" class="nav-link">联系</a>
            </div>
            <div class="nav-actions">
                <button class="btn btn-primary" onclick="scrollToSection('workspace')">开始使用</button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <span class="badge-icon">🚀</span>
                    <span>开源免费的智能文档助手</span>
                </div>
                <h1 class="hero-title">
                    让您的文档
                    <span class="gradient-text">智能化</span>
                </h1>
                <p class="hero-subtitle">
                    基于先进的RAG技术，AI Cousin能够理解您的文档内容，
                    提供精准的问答服务。完全开源免费，让知识管理变得前所未有的简单。
                </p>
                <div class="hero-actions">
                    <button class="btn btn-primary btn-large" onclick="scrollToSection('workspace')">
                        <i class="fas fa-rocket"></i>
                        立即体验
                    </button>
                    <button class="btn btn-secondary btn-large" onclick="playDemo()">
                        <i class="fas fa-play"></i>
                        观看演示
                    </button>
                </div>
                <div class="hero-stats">
                    <div class="stat-item">
                        <div class="stat-number" id="documentsCount">0</div>
                        <div class="stat-label">已处理文档</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="queriesCount">0</div>
                        <div class="stat-label">智能问答</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">开源</div>
                        <div class="stat-label">完全免费</div>
                    </div>
                </div>
            </div>
            <div class="hero-visual">
                <div class="visual-container">
                    <div class="floating-elements">
                        <div class="floating-card document-card">
                            <i class="fas fa-file-pdf"></i>
                            <span>PDF</span>
                        </div>
                        <div class="floating-card chat-card">
                            <i class="fas fa-comments"></i>
                            <span>智能问答</span>
                        </div>
                        <div class="floating-card brain-card">
                            <i class="fas fa-brain"></i>
                            <span>AI分析</span>
                        </div>
                    </div>
                    <div class="central-orb">
                        <div class="orb-inner">
                            <i class="fas fa-magic"></i>
                        </div>
                        <div class="orb-rings">
                            <div class="ring ring-1"></div>
                            <div class="ring ring-2"></div>
                            <div class="ring ring-3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">强大功能</h2>
                <p class="section-subtitle">为您的文档管理提供全方位的AI支持</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-upload"></i>
                    </div>
                    <h3 class="feature-title">智能文档上传</h3>
                    <p class="feature-description">支持PDF、Word、文本等多种格式，自动解析和向量化处理</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3 class="feature-title">语义搜索</h3>
                    <p class="feature-description">基于语义理解的智能搜索，找到最相关的内容片段</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="feature-title">智能问答</h3>
                    <p class="feature-description">基于文档内容的精准问答，提供详细的引用来源</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3 class="feature-title">数据库集成</h3>
                    <p class="feature-description">连接数据库，理解表结构，提供数据相关的智能问答</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Workspace Section -->
    <section class="workspace" id="workspace">
        <div class="container">
            <div class="workspace-header">
                <h2 class="workspace-title">开源智能工作台</h2>
                <div class="workspace-tabs">
                    <button class="tab-btn active" data-tab="chat">
                        <i class="fas fa-comments"></i>
                        智能问答
                    </button>
                    <button class="tab-btn" data-tab="documents">
                        <i class="fas fa-folder"></i>
                        文档管理
                    </button>
                    <button class="tab-btn" data-tab="database">
                        <i class="fas fa-database"></i>
                        数据库
                    </button>
                </div>
            </div>

            <!-- Chat Tab -->
            <div class="tab-content active" id="chat-tab">
                <div class="chat-workspace">
                    <div class="chat-sidebar">
                        <div class="sidebar-header">
                            <h3>对话历史</h3>
                            <button class="btn btn-icon" onclick="newChat()">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div class="chat-history" id="chatHistory">
                            <!-- Chat history items will be populated here -->
                        </div>
                    </div>
                    <div class="chat-main">
                        <div class="chat-header">
                            <div class="chat-info">
                                <h3>AI助手</h3>
                                <div class="status-indicator online">
                                    <span class="status-dot"></span>
                                    <span>在线</span>
                                </div>
                            </div>
                            <div class="chat-controls">
                                <div class="control-group">
                                    <label>检索源</label>
                                    <select id="sourceType" class="control-select">
                                        <option value="all">全部</option>
                                        <option value="documents">文档</option>
                                        <option value="database_schemas">数据库</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label>检索数量</label>
                                    <input type="number" id="topK" value="5" min="1" max="20" class="control-input">
                                </div>
                                <div class="control-group">
                                    <label>相似度: <span id="similarityValue">0.70</span></label>
                                    <input type="range" id="similarityThreshold" min="0" max="1" step="0.01" value="0.7" class="control-slider">
                                </div>
                            </div>
                        </div>
                        <div class="chat-messages" id="chatMessages">
                            <div class="welcome-message">
                                <div class="welcome-icon">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <h3>欢迎使用AI Cousin</h3>
                                <p>我是您的开源智能文档助手，可以帮您分析文档内容、回答问题。请先上传文档或连接数据库，然后开始提问。完全免费使用！</p>
                            </div>
                        </div>
                        <div class="chat-input-area">
                            <div class="input-container">
                                <textarea id="chatInput" placeholder="输入您的问题..." rows="1"></textarea>
                                <button id="sendButton" class="send-btn">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Documents Tab -->
            <div class="tab-content" id="documents-tab">
                <div class="documents-workspace">
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <h3>拖拽文件到此处或点击上传</h3>
                            <p>支持 PDF、DOCX、TXT、MD 格式，最大 10MB</p>
                            <input type="file" id="fileInput" accept=".pdf,.docx,.txt,.md" hidden>
                            <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                                选择文件
                            </button>
                        </div>
                    </div>
                    <div class="documents-list" id="documentsList">
                        <!-- Documents will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Database Tab -->
            <div class="tab-content" id="database-tab">
                <div class="database-workspace">
                    <div class="database-form">
                        <h3>数据库连接</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label>数据库类型</label>
                                <select id="dbType" class="form-input">
                                    <option value="mysql">MySQL</option>
                                    <option value="postgresql">PostgreSQL</option>
                                    <option value="sqlserver">SQL Server</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>主机地址</label>
                                <input type="text" id="dbHost" class="form-input" placeholder="localhost">
                            </div>
                            <div class="form-group">
                                <label>端口</label>
                                <input type="number" id="dbPort" class="form-input" placeholder="3306">
                            </div>
                            <div class="form-group">
                                <label>数据库名</label>
                                <input type="text" id="dbName" class="form-input" placeholder="database_name">
                            </div>
                            <div class="form-group">
                                <label>用户名</label>
                                <input type="text" id="dbUsername" class="form-input" placeholder="username">
                            </div>
                            <div class="form-group">
                                <label>密码</label>
                                <input type="password" id="dbPassword" class="form-input" placeholder="password">
                            </div>
                        </div>
                        <div class="form-actions">
                            <button class="btn btn-primary" onclick="connectDatabase()">
                                <i class="fas fa-plug"></i>
                                连接数据库
                            </button>
                        </div>
                    </div>
                    <div class="database-tables" id="databaseTables">
                        <!-- Tables will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- Status Bar -->
    <div class="status-bar" id="statusBar">
        <div class="status-item">
            <span class="status-label">系统状态:</span>
            <span class="status-value" id="systemStatus">正常</span>
        </div>
        <div class="status-item">
            <span class="status-label">Embedding:</span>
            <span class="status-value" id="embeddingStatus">加载中...</span>
        </div>
        <div class="status-item">
            <span class="status-label">文档数量:</span>
            <span class="status-value" id="documentCount">0</span>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p class="loading-text">处理中...</p>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <script src="js/apple-app.js?v=1.2"></script>
</body>
</html>
