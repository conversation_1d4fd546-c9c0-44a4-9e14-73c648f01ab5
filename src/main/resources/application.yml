spring:
  application:
    name: ai-cousin-rag
  ai:
    openai:
      api-key: ${DEEPSEEK_API_KEY:***********************************}
      base-url: ${DEEPSEEK_BASE_URL:https://api.deepseek.com}
      chat:
        options:
          model: ${DEEPSEEK_CHAT_MODEL:deepseek-chat}
          temperature: 0.7
          max-tokens: 1000
      embedding:
        options:
          # DeepSeek目前可能不支持embedding模型，系统会自动使用Milvus fallback
          model: ${DEEPSEEK_EMBEDDING_MODEL:deepseek-embedding-v1}
          # 注意：如果DeepSeek不支持embedding，系统会自动切换到Milvus fallback模式

# Milvus Configuration (Remote Service)
milvus:
  host: ${MILVUS_HOST:************}
  port: ${MILVUS_PORT:2323}
  database: ${MILVUS_DATABASE:default}
  # 如果远程Milvus需要认证，可以添加以下配置
  # username: ${MILVUS_USERNAME:}
  # password: ${MILVUS_PASSWORD:}
  # token: ${MILVUS_TOKEN:}
  documentCollection:
    name: document_vectors
    dimension: 1536
    metric-type: COSINE
    index-type: IVF_FLAT
    nlist: 1024
  schemaCollection: # New collection for database schema
    name: db_schema_vectors
    dimension: 1536 # Should match your embedding model's dimension
    metric-type: COSINE
    index-type: IVF_FLAT
    nlist: 1024

# External Embedding Service Configuration
external:
  embedding:
    enabled: ${EXTERNAL_EMBEDDING_ENABLED:false}  # 设置为true启用外部向量化服务
    base-url: ${EXTERNAL_EMBEDDING_BASE_URL:https://ebskdz-mlxchs-8000.app.cloudstudio.work}
    endpoint: ${EXTERNAL_EMBEDDING_ENDPOINT:/v1/embeddings}
    connect-timeout: ${EXTERNAL_EMBEDDING_CONNECT_TIMEOUT:5000}
    read-timeout: ${EXTERNAL_EMBEDDING_READ_TIMEOUT:30000}
    max-retries: ${EXTERNAL_EMBEDDING_MAX_RETRIES:3}
    batch-size: ${EXTERNAL_EMBEDDING_BATCH_SIZE:10}
    dimension: ${EXTERNAL_EMBEDDING_DIMENSION:512}
    model: ${EXTERNAL_EMBEDDING_MODEL:BAAI/bge-small-zh-v1.5}
    api-key: ${EXTERNAL_EMBEDDING_API_KEY:}  # 如果需要认证

# RAG Configuration
rag:
  chunk:
    size: 1000
    overlap: 200
  retrieval:
    top-k: 5
    similarity-threshold: 0.3  # 降低阈值以适应fallback embedding

# Server Configuration
server:
  port: 8080

# Logging
logging:
  level:
    org.springframework.ai: DEBUG
    io.milvus: INFO
    com.example.aicousin: DEBUG
