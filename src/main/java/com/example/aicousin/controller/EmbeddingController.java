package com.example.aicousin.controller;

import com.example.aicousin.service.EmbeddingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 向量化服务管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/embedding")
@RequiredArgsConstructor
public class EmbeddingController {
    
    private final EmbeddingService embeddingService;
    
    /**
     * 获取向量化服务状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            response.put("status", "success");
            response.put("embeddingStatus", embeddingService.getEmbeddingStatus());
            response.put("isEmbeddingAvailable", embeddingService.isEmbeddingModelAvailable());
            response.put("isExternalAvailable", embeddingService.isExternalEmbeddingAvailable());
            response.put("isDeepSeekAvailable", embeddingService.isDeepSeekEmbeddingAvailable());
            response.put("detailedStatus", embeddingService.getDetailedStatus());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Failed to get embedding status", e);
            response.put("status", "error");
            response.put("message", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 重试向量化服务
     */
    @PostMapping("/retry")
    public ResponseEntity<Map<String, Object>> retryServices() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            embeddingService.retryEmbeddingServices();
            
            response.put("status", "success");
            response.put("message", "Embedding services retry completed");
            response.put("newStatus", embeddingService.getEmbeddingStatus());
            response.put("detailedStatus", embeddingService.getDetailedStatus());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Failed to retry embedding services", e);
            response.put("status", "error");
            response.put("message", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 测试向量化生成
     */
    @PostMapping("/test")
    public ResponseEntity<Map<String, Object>> testEmbedding(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String text = request.getOrDefault("text", "这是一个测试文本");
            
            long startTime = System.currentTimeMillis();
            List<Float> embedding = embeddingService.generateEmbedding(text);
            long endTime = System.currentTimeMillis();
            
            response.put("status", "success");
            response.put("text", text);
            response.put("embeddingDimension", embedding.size());
            response.put("processingTimeMs", endTime - startTime);
            response.put("embeddingStatus", embeddingService.getEmbeddingStatus());
            response.put("sampleValues", embedding.subList(0, Math.min(5, embedding.size())));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Failed to test embedding generation", e);
            response.put("status", "error");
            response.put("message", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 批量测试向量化生成
     */
    @PostMapping("/test-batch")
    public ResponseEntity<Map<String, Object>> testBatchEmbedding(@RequestBody Map<String, List<String>> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<String> texts = request.getOrDefault("texts", 
                List.of("测试文本1", "测试文本2", "测试文本3"));
            
            long startTime = System.currentTimeMillis();
            List<List<Float>> embeddings = embeddingService.generateEmbeddings(texts);
            long endTime = System.currentTimeMillis();
            
            response.put("status", "success");
            response.put("textCount", texts.size());
            response.put("embeddingCount", embeddings.size());
            response.put("embeddingDimension", embeddings.isEmpty() ? 0 : embeddings.get(0).size());
            response.put("processingTimeMs", endTime - startTime);
            response.put("avgTimePerText", (endTime - startTime) / (double) texts.size());
            response.put("embeddingStatus", embeddingService.getEmbeddingStatus());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Failed to test batch embedding generation", e);
            response.put("status", "error");
            response.put("message", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取缓存统计信息
     */
    @GetMapping("/cache/stats")
    public ResponseEntity<Map<String, Object>> getCacheStats() {
        Map<String, Object> response = new HashMap<>();

        try {
            var cacheStats = embeddingService.getCacheStats();

            response.put("status", "success");
            response.put("cacheStats", Map.of(
                "hits", cacheStats.getHits(),
                "misses", cacheStats.getMisses(),
                "hitRate", String.format("%.2f%%", cacheStats.getHitRate() * 100),
                "size", cacheStats.getSize()
            ));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to get cache stats", e);
            response.put("status", "error");
            response.put("message", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 清空缓存
     */
    @PostMapping("/cache/clear")
    public ResponseEntity<Map<String, Object>> clearCache() {
        Map<String, Object> response = new HashMap<>();

        try {
            embeddingService.clearCache();

            response.put("status", "success");
            response.put("message", "Cache cleared successfully");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to clear cache", e);
            response.put("status", "error");
            response.put("message", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取性能指标
     */
    @GetMapping("/metrics")
    public ResponseEntity<Map<String, Object>> getMetrics() {
        Map<String, Object> response = new HashMap<>();

        try {
            // 这里需要注入EmbeddingMetricsService
            response.put("status", "success");
            response.put("message", "Metrics endpoint - implementation needed");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to get metrics", e);
            response.put("status", "error");
            response.put("message", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 异步测试embedding生成
     */
    @PostMapping("/test-async")
    public ResponseEntity<Map<String, Object>> testAsyncEmbedding(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();

        try {
            String text = request.getOrDefault("text", "这是一个异步测试文本");

            // 启动异步任务
            var future = embeddingService.generateEmbeddingAsync(text);

            response.put("status", "success");
            response.put("message", "Async embedding generation started");
            response.put("text", text);
            response.put("taskId", "async-" + System.currentTimeMillis());

            // 注意：实际应用中应该返回任务ID，让客户端轮询结果
            // 这里为了演示，我们等待结果
            var result = future.get();
            response.put("embeddingDimension", result.size());
            response.put("sampleValues", result.subList(0, Math.min(5, result.size())));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to test async embedding generation", e);
            response.put("status", "error");
            response.put("message", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 更新外部向量化服务配置
     */
    @PostMapping("/external/config")
    public ResponseEntity<Map<String, Object>> updateExternalConfig(@RequestBody Map<String, Object> config) {
        Map<String, Object> response = new HashMap<>();

        try {
            Boolean enabled = (Boolean) config.get("enabled");
            String baseUrl = (String) config.get("baseUrl");

            log.info("🔧 更新外部向量化服务配置: enabled={}, baseUrl={}", enabled, baseUrl);

            // 注意：这里只是演示，实际的动态配置需要更复杂的实现
            // 因为ExternalEmbeddingProperties是在启动时加载的
            response.put("status", "info");
            response.put("message", "配置更新请求已接收");
            response.put("note", "由于配置是在启动时加载的，请设置环境变量并重启服务以使配置生效");
            response.put("envVars", Map.of(
                "EXTERNAL_EMBEDDING_ENABLED", enabled != null ? enabled.toString() : "false",
                "EXTERNAL_EMBEDDING_BASE_URL", baseUrl != null ? baseUrl : "http://0.0.0.0:8000"
            ));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to update external config", e);
            response.put("status", "error");
            response.put("message", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 测试外部向量化服务连接
     */
    @PostMapping("/external/test")
    public ResponseEntity<Map<String, Object>> testExternalService(@RequestBody Map<String, String> config) {
        Map<String, Object> response = new HashMap<>();

        try {
            String baseUrl = config.get("baseUrl");
            if (baseUrl == null || baseUrl.trim().isEmpty()) {
                response.put("status", "error");
                response.put("message", "请提供有效的服务URL");
                return ResponseEntity.badRequest().body(response);
            }

            log.info("🔍 测试外部向量化服务: {}", baseUrl);

            // 这里可以添加实际的连接测试逻辑
            // 例如发送一个简单的HTTP请求到指定URL
            try {
                java.net.URL url = new java.net.URL(baseUrl + "/v1/embeddings");
                java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);

                int responseCode = connection.getResponseCode();

                if (responseCode == 200 || responseCode == 405) { // 405 Method Not Allowed is also OK for GET on POST endpoint
                    response.put("status", "success");
                    response.put("message", "外部向量化服务连接成功");
                    response.put("responseCode", responseCode);
                } else {
                    response.put("status", "warning");
                    response.put("message", "服务响应异常，响应码: " + responseCode);
                    response.put("responseCode", responseCode);
                }

            } catch (java.net.ConnectException e) {
                response.put("status", "error");
                response.put("message", "无法连接到服务，请检查URL和服务状态");
            } catch (java.net.SocketTimeoutException e) {
                response.put("status", "error");
                response.put("message", "连接超时，请检查网络和服务状态");
            } catch (Exception e) {
                response.put("status", "error");
                response.put("message", "连接测试失败: " + e.getMessage());
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to test external service", e);
            response.put("status", "error");
            response.put("message", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
