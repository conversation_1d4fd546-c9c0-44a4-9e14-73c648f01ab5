package com.example.aicousin.controller;

import com.example.aicousin.dto.DatabaseConnectionRequest;
import com.example.aicousin.dto.TableSchemaInfo;
import com.example.aicousin.service.DatabaseSchemaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/database")
@RequiredArgsConstructor
public class DatabaseController {

    private final DatabaseSchemaService databaseSchemaService;

    /**
     * Endpoint to test database connection and get a list of available tables.
     * @param request DatabaseConnectionRequest containing connection details.
     * @return ResponseEntity with list of table names or an error message.
     */
    @PostMapping("/connect")
    public ResponseEntity<?> connectAndListTables(@RequestBody DatabaseConnectionRequest request) {
        try {
            log.info("Received database connection request for dbType: {}, host: {}, database: {}",
                    request.getDbType(), request.getHost(), request.getDatabaseName());
            List<String> tables = databaseSchemaService.getAvailableTables(request);
            return ResponseEntity.ok(Map.of("status", "success", "tables", tables));
        } catch (IllegalArgumentException e) {
            log.error("Invalid database type or connection parameters: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("status", "error", "message", e.getMessage()));
        } catch (SQLException e) {
            log.error("Database connection or metadata retrieval failed: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("status", "error", "message", "Failed to connect to database or retrieve tables: " + e.getMessage()));
        }
    }

    /**
     * Endpoint to collect and vectorize schema for a specific table.
     * @param request Map containing database connection details and tableName.
     * @return ResponseEntity with TableSchemaInfo or an error message.
     */
    @PostMapping("/collect-schema")
    public ResponseEntity<?> collectTableSchema(@RequestBody Map<String, Object> request) {
        try {
            DatabaseConnectionRequest connectionRequest = new DatabaseConnectionRequest();
            connectionRequest.setDbType((String) request.get("dbType"));
            connectionRequest.setHost((String) request.get("host"));
            connectionRequest.setPort((Integer) request.get("port"));
            connectionRequest.setDatabaseName((String) request.get("databaseName"));
            connectionRequest.setUsername((String) request.get("username"));
            connectionRequest.setPassword((String) request.get("password"));

            String tableName = (String) request.get("tableName");
            if (tableName == null || tableName.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("status", "error", "message", "Table name is required."));
            }

            log.info("Received request to collect schema for table {} from dbType: {}, host: {}, database: {}",
                    tableName, connectionRequest.getDbType(), connectionRequest.getHost(), connectionRequest.getDatabaseName());

            TableSchemaInfo schemaInfo = databaseSchemaService.getAndVectorizeTableSchema(connectionRequest, tableName);
            return ResponseEntity.ok(Map.of("status", "success", "schemaInfo", schemaInfo));
        } catch (IllegalArgumentException e) {
            log.error("Invalid request parameters: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("status", "error", "message", e.getMessage()));
        } catch (SQLException e) {
            log.error("Failed to collect or vectorize table schema: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("status", "error", "message", "Failed to collect table schema: " + e.getMessage()));
        } catch (Exception e) {
            log.error("An unexpected error occurred: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("status", "error", "message", "An unexpected error occurred: " + e.getMessage()));
        }
    }
} 