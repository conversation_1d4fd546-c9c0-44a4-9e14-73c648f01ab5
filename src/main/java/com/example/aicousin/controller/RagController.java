package com.example.aicousin.controller;

import com.example.aicousin.dto.DocumentUploadResponse;
import com.example.aicousin.dto.QueryRequest;
import com.example.aicousin.dto.QueryResponse;
import com.example.aicousin.service.RagService;
import com.example.aicousin.service.EmbeddingService;
import com.example.aicousin.service.RetrievalService;
import com.example.aicousin.service.MilvusVectorService;
import com.example.aicousin.service.DocumentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/rag")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class RagController {

    private final RagService ragService;
    private final EmbeddingService embeddingService;
    private final RetrievalService retrievalService;
    private final MilvusVectorService milvusVectorService;
    private final DocumentService documentService;

    /**
     * Upload and process a document
     */
    @PostMapping(value = "/documents", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<DocumentUploadResponse> uploadDocument(
            @RequestParam("file") MultipartFile file) {
        
        log.info("Received document upload request: {}", file.getOriginalFilename());
        
        if (file.isEmpty()) {
            DocumentUploadResponse response = DocumentUploadResponse.builder()
                    .documentName(file.getOriginalFilename())
                    .chunksCreated(0)
                    .processingTimeMs(0)
                    .status("FAILED")
                    .message("File is empty")
                    .build();
            return ResponseEntity.badRequest().body(response);
        }
        
        // Check file size (limit to 5MB)
        if (file.getSize() > 5 * 1024 * 1024) {
            DocumentUploadResponse response = DocumentUploadResponse.builder()
                    .documentName(file.getOriginalFilename())
                    .chunksCreated(0)
                    .processingTimeMs(0)
                    .status("FAILED")
                    .message("File size exceeds 5MB limit")
                    .build();
            return ResponseEntity.badRequest().body(response);
        }
        
        DocumentUploadResponse response = ragService.processAndStoreDocument(file);
        
        if ("SUCCESS".equals(response.getStatus())) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Query the RAG system
     */
    @PostMapping("/query")
    public ResponseEntity<QueryResponse> query(@Valid @RequestBody QueryRequest request) {
        log.info("Received query request: {}", request.getQuery());
        
        QueryResponse response = ragService.query(request);
        return ResponseEntity.ok(response);
    }



    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("RAG service is running");
    }

    /**
     * Get embedding status
     */
    @GetMapping("/embedding/status")
    public ResponseEntity<Map<String, Object>> getEmbeddingStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("status", embeddingService.getEmbeddingStatus());
        status.put("available", embeddingService.isEmbeddingModelAvailable());
        status.put("usingFallback", !embeddingService.isEmbeddingModelAvailable());
        return ResponseEntity.ok(status);
    }

    /**
     * Retry embedding model
     */
    @PostMapping("/embedding/retry")
    public ResponseEntity<Map<String, Object>> retryEmbeddingModel() {
        embeddingService.retryEmbeddingServices();

        Map<String, Object> response = new HashMap<>();
        response.put("message", "Embedding services retry initiated");
        response.put("status", embeddingService.getEmbeddingStatus());

        return ResponseEntity.ok(response);
    }

    /**
     * 调试检索结果 - 查看实际的相似度分数
     */
    @PostMapping("/debug/retrieval")
    public ResponseEntity<Map<String, Object>> debugRetrieval(@RequestBody Map<String, Object> request) {
        String query = (String) request.get("query");
        Integer topK = (Integer) request.getOrDefault("topK", 10);
        Double threshold = (Double) request.getOrDefault("threshold", 0.0); // 使用很低的阈值
        String dbName = (String) request.get("dbName");
        String tableName = (String) request.get("tableName");
        RetrievalService.SearchSourceType sourceType = RetrievalService.SearchSourceType.valueOf(
                (String) request.getOrDefault("sourceType", RetrievalService.SearchSourceType.DOCUMENTS.name()));

        try {
            List<QueryResponse.RetrievedChunk> chunks = retrievalService.retrieveRelevantChunks(
                    query, topK, threshold, null, dbName, tableName, sourceType);

            Map<String, Object> debugInfo = new HashMap<>();
            debugInfo.put("query", query);
            debugInfo.put("totalRetrieved", chunks.size());
            debugInfo.put("embeddingStatus", embeddingService.getEmbeddingStatus());
            debugInfo.put("usingFallback", !embeddingService.isEmbeddingModelAvailable());

            List<Map<String, Object>> chunkDetails = chunks.stream()
                    .map(chunk -> {
                        Map<String, Object> detail = new HashMap<>();
                        detail.put("documentName", chunk.getDocumentName());
                        detail.put("chunkIndex", chunk.getChunkIndex());
                        detail.put("similarity", chunk.getSimilarity());
                        detail.put("contentPreview", chunk.getContent().substring(0,
                                Math.min(100, chunk.getContent().length())) + "...");
                        return detail;
                    })
                    .collect(Collectors.toList());

            debugInfo.put("chunks", chunkDetails);

            return ResponseEntity.ok(debugInfo);

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", e.getMessage());
            error.put("query", query);
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 调试Milvus数据 - 检查集合中的数据数量
     */
    @GetMapping("/debug/milvus")
    public ResponseEntity<Map<String, Object>> debugMilvus() {
        try {
            Map<String, Object> debugInfo = new HashMap<>();

            debugInfo.put("message", "Milvus调试信息");
            debugInfo.put("timestamp", System.currentTimeMillis());

            // 检查embedding状态
            debugInfo.put("embeddingStatus", embeddingService.getEmbeddingStatus());
            debugInfo.put("usingFallback", !embeddingService.isEmbeddingModelAvailable());

            return ResponseEntity.ok(debugInfo);

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 获取已上传的文档列表
     */
    @GetMapping("/documents")
    public ResponseEntity<Map<String, Object>> getDocuments() {
        try {
            log.info("📋 获取文档列表请求");

            // 从DocumentService获取真实的文档列表
            List<DocumentService.DocumentInfo> documents = documentService.getAllDocuments();

            // 转换为前端需要的格式
            List<Map<String, Object>> documentMaps = new ArrayList<>();
            for (DocumentService.DocumentInfo doc : documents) {
                documentMaps.add(doc.toMap());
            }

            // 获取Milvus统计信息
            Map<String, Object> milvusStats = milvusVectorService.getCollectionStats();

            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("documents", documentMaps);
            response.put("total", documents.size());
            response.put("timestamp", System.currentTimeMillis());
            response.put("stats", documentService.getDocumentStats());
            response.put("milvusStats", milvusStats);

            log.info("✅ 返回 {} 个文档", documents.size());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("❌ 获取文档列表失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", e.getMessage());
            error.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 删除文档
     */
    @DeleteMapping("/documents/{documentId}")
    public ResponseEntity<Map<String, Object>> deleteDocument(@PathVariable String documentId) {
        try {
            log.info("🗑️ 删除文档请求: {}", documentId);

            // 检查文档是否存在
            if (!documentService.documentExists(documentId)) {
                Map<String, Object> response = new HashMap<>();
                response.put("message", "文档不存在");
                response.put("documentId", documentId);
                response.put("status", "NOT_FOUND");
                response.put("timestamp", System.currentTimeMillis());

                log.warn("⚠️ 尝试删除不存在的文档: {}", documentId);
                return ResponseEntity.status(404).body(response);
            }

            // 从DocumentService删除文档信息
            boolean deleted = documentService.deleteDocument(documentId);

            // TODO: 从Milvus删除相关的向量数据（需要实现deleteDocument方法）
            log.info("📝 文档信息已从DocumentService删除: {}", documentId);

            Map<String, Object> response = new HashMap<>();
            if (deleted) {
                response.put("message", "文档删除成功");
                response.put("status", "SUCCESS");
                log.info("✅ 文档删除成功: {}", documentId);
            } else {
                response.put("message", "文档删除失败");
                response.put("status", "FAILED");
                log.error("❌ 文档删除失败: {}", documentId);
            }

            response.put("documentId", documentId);
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("❌ 删除文档异常: {}", documentId, e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", e.getMessage());
            error.put("documentId", documentId);
            error.put("status", "ERROR");
            error.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(500).body(error);
        }
    }

    @PostMapping("/milvus/fix-structure")
    public ResponseEntity<Map<String, Object>> fixMilvusStructure() {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("🔧 Attempting to fix Milvus collection structure...");

            boolean wasRebuilt = milvusVectorService.checkAndFixCollectionStructure();

            response.put("status", "success");
            response.put("message", wasRebuilt ? "Collection structure was rebuilt" : "Collection structure is valid");
            response.put("rebuilt", wasRebuilt);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to fix Milvus structure", e);
            response.put("status", "error");
            response.put("message", "Failed to fix collection structure: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PostMapping("/milvus/rebuild-collection")
    public ResponseEntity<Map<String, Object>> rebuildMilvusCollection() {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("🔄 Rebuilding Milvus document collection...");

            milvusVectorService.rebuildDocumentCollection();

            response.put("status", "success");
            response.put("message", "Document collection has been rebuilt successfully");
            response.put("warning", "All existing document data has been lost and needs to be re-uploaded");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to rebuild Milvus collection", e);
            response.put("status", "error");
            response.put("message", "Failed to rebuild collection: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 手动触发从Milvus恢复文档元数据
     */
    @PostMapping("/documents/recover")
    public ResponseEntity<Map<String, Object>> recoverDocumentsFromMilvus() {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("🔄 手动触发文档元数据恢复...");

            // 获取恢复前的文档数量
            int beforeCount = documentService.getDocumentCount();

            // 手动调用恢复方法
            documentService.recoverDocumentsFromMilvus();

            int afterCount = documentService.getDocumentCount();
            int recoveredCount = afterCount - beforeCount;

            response.put("status", "success");
            response.put("message", "文档元数据恢复完成");
            response.put("beforeCount", beforeCount);
            response.put("afterCount", afterCount);
            response.put("recoveredCount", recoveredCount);
            response.put("timestamp", System.currentTimeMillis());

            log.info("✅ 文档元数据恢复完成，恢复了 {} 个文档", recoveredCount);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("❌ 文档元数据恢复失败", e);
            response.put("status", "error");
            response.put("message", "文档元数据恢复失败: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(500).body(response);
        }
    }
}
