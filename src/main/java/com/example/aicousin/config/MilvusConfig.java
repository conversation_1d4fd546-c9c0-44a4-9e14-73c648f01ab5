package com.example.aicousin.config;

import io.milvus.client.MilvusServiceClient;
import io.milvus.param.ConnectParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

@Slf4j
@Configuration
public class MilvusConfig {

    @Value("${milvus.host}")
    private String host;

    @Value("${milvus.port}")
    private int port;

    @Value("${milvus.username:}")
    private String username;

    @Value("${milvus.password:}")
    private String password;

    @Value("${milvus.token:}")
    private String token;

    @Value("${milvus.database:default}")
    private String database;

    @Bean
    public MilvusServiceClient milvusClient() {
        log.info("Connecting to remote Milvus at {}:{}", host, port);

        ConnectParam.Builder connectParamBuilder = ConnectParam.newBuilder()
                .withHost(host)
                .withPort(port);

        // 添加数据库配置
        if (StringUtils.hasText(database) && !"default".equals(database)) {
            connectParamBuilder.withDatabaseName(database);
            log.info("Using database: {}", database);
        }

        // 添加认证配置（注意：Milvus SDK 2.3.4可能不支持所有认证方法）
        if (StringUtils.hasText(token)) {
            try {
                connectParamBuilder.withToken(token);
                log.info("Using token authentication");
            } catch (Exception e) {
                log.warn("Token authentication not supported in this Milvus SDK version: {}", e.getMessage());
            }
        } else if (StringUtils.hasText(username) && StringUtils.hasText(password)) {
            log.warn("Username/password authentication not supported in Milvus SDK 2.3.4");
            log.info("Please use token authentication or upgrade to a newer SDK version");
        } else {
            log.info("No authentication configured, using anonymous connection");
        }

        ConnectParam connectParam = connectParamBuilder.build();

        try {
            MilvusServiceClient client = new MilvusServiceClient(connectParam);
            log.info("✅ Successfully connected to remote Milvus service");
            return client;
        } catch (Exception e) {
            log.error("❌ Failed to connect to remote Milvus service at {}:{}", host, port, e);
            throw new RuntimeException("Failed to connect to Milvus: " + e.getMessage(), e);
        }
    }
}
