package com.example.aicousin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "milvus")
public class MilvusProperties {

    private String host;
    private int port;
    private String username;
    private String password;
    private String token;
    private Collection documentCollection;
    private Collection schemaCollection; // New collection for database schema

    @Data
    public static class Collection {
        private String name;
        private int dimension;
        private String metricType;
        private String indexType;
        private int nlist;
    }
}
