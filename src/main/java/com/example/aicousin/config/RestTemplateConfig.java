package com.example.aicousin.config;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * RestTemplate配置
 */
@Configuration
@RequiredArgsConstructor
public class RestTemplateConfig {
    
    private final ExternalEmbeddingProperties embeddingProperties;
    
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        return builder
            .setConnectTimeout(Duration.ofMillis(embeddingProperties.getConnectTimeout()))
            .setReadTimeout(Duration.ofMillis(embeddingProperties.getReadTimeout()))
            .build();
    }
}
