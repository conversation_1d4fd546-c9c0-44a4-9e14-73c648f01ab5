package com.example.aicousin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 外部向量化服务配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "external.embedding")
public class ExternalEmbeddingProperties {
    
    /**
     * 是否启用外部向量化服务
     */
    private boolean enabled = false;
    
    /**
     * 外部向量化服务的基础URL
     */
    private String baseUrl = "http://0.0.0.0:8000";
    
    /**
     * API端点路径
     */
    private String endpoint = "/v1/embeddings";
    
    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 5000;
    
    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 30000;
    
    /**
     * 最大重试次数
     */
    private int maxRetries = 3;
    
    /**
     * 批处理大小
     */
    private int batchSize = 10;
    
    /**
     * 向量维度（从API响应中获取，这里作为默认值）
     */
    private int dimension = 512;
    
    /**
     * 模型名称（从API响应中获取）
     */
    private String model = "BAAI/bge-small-zh-v1.5";
    
    /**
     * API密钥（如果需要认证）
     */
    private String apiKey;
    
    /**
     * 获取完整的API URL
     */
    public String getFullUrl() {
        return baseUrl + endpoint;
    }
}
