package com.example.aicousin.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ChatClient配置类
 * 
 * 注意：ChatClient在Spring AI 1.0.0-M4中可能不可用
 * 如果您的版本支持ChatClient，可以取消注释相关代码
 */
@Slf4j
@Configuration
public class ChatClientConfig {

    /**
     * 验证ChatModel配置
     */
    @Bean
    public String chatModelInfo(ChatModel chatModel) {
        log.info("✅ ChatModel配置成功: {}", chatModel.getClass().getSimpleName());
        log.info("💡 当前Spring AI版本使用ChatModel，不是ChatClient");
        return "ChatModel configured successfully";
    }
}
