package com.example.aicousin.service;

import com.example.aicousin.dto.DocumentUploadResponse;
import com.example.aicousin.dto.QueryRequest;
import com.example.aicousin.dto.QueryResponse;
import com.example.aicousin.entity.DocumentChunk;
import com.example.aicousin.service.RetrievalService.SearchSourceType; // Import SearchSourceType
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class RagService {

    private final DocumentProcessingService documentProcessingService;
    private final MilvusVectorService milvusVectorService;
    private final EmbeddingService embeddingService;
    private final GenerationService generationService;
    private final DocumentService documentService;
    private final RetrievalService retrievalService;

    /**
     * Process and store a document
     */
    public DocumentUploadResponse processAndStoreDocument(MultipartFile file) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("Starting document processing for: {}", file.getOriginalFilename());

            // 先添加文档信息到管理系统
            DocumentService.DocumentInfo docInfo = documentService.addDocument(
                file.getOriginalFilename(),
                file.getOriginalFilename(),
                file.getSize(),
                "", // 文件路径，这里可以根据需要存储实际路径
                file.getContentType()
            );

            // Process document into chunks with embeddings
            List<DocumentChunk> chunks = documentProcessingService.processDocument(file);

            if (chunks.isEmpty()) {
                // 更新文档状态为失败
                documentService.updateDocumentStatus(docInfo.getId(), "处理失败");

                return DocumentUploadResponse.builder()
                        .documentId(docInfo.getId())
                        .documentName(file.getOriginalFilename())
                        .chunksCreated(0)
                        .processingTimeMs(System.currentTimeMillis() - startTime)
                        .status("FAILED")
                        .message("No content could be extracted from the document")
                        .embeddingStatus(embeddingService.getEmbeddingStatus())
                        .usingFallbackEmbedding(!embeddingService.isEmbeddingModelAvailable())
                        .build();
            }

            // Store chunks in Milvus
            milvusVectorService.insertDocumentChunks(chunks);

            // 更新文档信息
            documentService.updateDocumentChunks(docInfo.getId(), chunks.size());
            documentService.updateDocumentStatus(docInfo.getId(), "已处理");

            log.info("Successfully processed document: {} with {} chunks",
                    file.getOriginalFilename(), chunks.size());

            return DocumentUploadResponse.builder()
                    .documentId(docInfo.getId())
                    .documentName(file.getOriginalFilename())
                    .chunksCreated(chunks.size())
                    .processingTimeMs(System.currentTimeMillis() - startTime)
                    .status("SUCCESS")
                    .message("Document processed and stored successfully")
                    .embeddingStatus(embeddingService.getEmbeddingStatus())
                    .usingFallbackEmbedding(!embeddingService.isEmbeddingModelAvailable())
                    .build();
                    
        } catch (Exception e) {
            log.error("Error processing document: {}", file.getOriginalFilename(), e);
            
            return DocumentUploadResponse.builder()
                    .documentName(file.getOriginalFilename())
                    .chunksCreated(0)
                    .processingTimeMs(System.currentTimeMillis() - startTime)
                    .status("FAILED")
                    .message("Error processing document: " + e.getMessage())
                    .build();
        }
    }

    /**
     * Query the RAG system
     */
    public QueryResponse query(QueryRequest request) {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("Processing query: {}", request.getQuery());
            
            // Determine search source type
            SearchSourceType sourceType;
            try {
                sourceType = request.getSourceType() != null && !request.getSourceType().isEmpty() 
                             ? SearchSourceType.valueOf(request.getSourceType().toUpperCase()) 
                             : SearchSourceType.ALL; // Default to ALL if not specified
            } catch (IllegalArgumentException e) {
                log.warn("Invalid search source type: {}. Defaulting to ALL.", request.getSourceType());
                sourceType = SearchSourceType.ALL;
            }

            // Retrieve relevant chunks from specified sources
            List<QueryResponse.RetrievedChunk> retrievedChunks = retrievalService.retrieveRelevantChunks(
                    request.getQuery(),
                    request.getTopK(),
                    request.getSimilarityThreshold(),
                    request.getDocumentId(),
                    request.getDbName(),
                    request.getTableName(),
                    sourceType
            );

            // Generate answer based on retrieved chunks
            String answer;
            if (retrievedChunks.isEmpty()) {
                // 如果没有检索到相关文档，使用直接聊天模式
                log.info("🤖 没有检索到相关内容，使用直接聊天模式");
                answer = generationService.generateDirectAnswer(request.getQuery());
            } else {
                // 基于检索到的内容生成回答
                log.info("📚 基于 {} 个检索到的片段生成回答", retrievedChunks.size());
                answer = generationService.generateAnswer(request.getQuery(), retrievedChunks);
            }
            
            log.info("Query processed successfully. Retrieved {} chunks", retrievedChunks.size());
            
            return QueryResponse.builder()
                    .answer(answer)
                    .query(request.getQuery())
                    .retrievedChunks(retrievedChunks)
                    .processingTimeMs(System.currentTimeMillis() - startTime)
                    .embeddingStatus(embeddingService.getEmbeddingStatus())
                    .usingFallbackEmbedding(!embeddingService.isEmbeddingModelAvailable())
                    .build();
                    
        } catch (Exception e) {
            log.error("Error processing query: {}", request.getQuery(), e);
            
            return QueryResponse.builder()
                    .answer("I apologize, but I encountered an error while processing your query. Please try again.")
                    .query(request.getQuery())
                    .retrievedChunks(List.of())
                    .processingTimeMs(System.currentTimeMillis() - startTime)
                    .build();
        }
    }

    /**
     * Delete a document and all its chunks
     */
    public void deleteDocument(String documentId) {
        try {
            log.info("Deleting document: {}", documentId);
            milvusVectorService.deleteDocumentChunks(documentId);
            log.info("Successfully deleted document: {}", documentId);
        } catch (Exception e) {
            log.error("Error deleting document: {}", documentId, e);
            throw new RuntimeException("Failed to delete document: " + e.getMessage());
        }
    }

    /**
     * Delete schema chunks for a specific database and table.
     * @param dbName The name of the database.
     * @param tableName The name of the table.
     */
    public void deleteDatabaseSchema(String dbName, String tableName) {
        try {
            log.info("Deleting schema for database: {} and table: {}", dbName, tableName);
            milvusVectorService.deleteSchemaChunks(dbName, tableName);
            log.info("Successfully deleted schema for database: {} and table: {}", dbName, tableName);
        } catch (Exception e) {
            log.error("Error deleting schema for database {} and table {}: {}", dbName, tableName, e);
            throw new RuntimeException("Failed to delete schema: " + e.getMessage());
        }
    }
}
