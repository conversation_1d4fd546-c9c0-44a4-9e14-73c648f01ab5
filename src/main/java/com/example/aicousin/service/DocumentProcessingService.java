package com.example.aicousin.service;

import com.example.aicousin.config.RagProperties;
import com.example.aicousin.entity.DocumentChunk;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.pdf.PagePdfDocumentReader;
import org.springframework.ai.reader.tika.TikaDocumentReader;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentProcessingService {

    private final RagProperties ragProperties;
    private final EmbeddingService embeddingService;

    /**
     * Process uploaded file and create document chunks with embeddings
     */
    public List<DocumentChunk> processDocument(MultipartFile file) throws IOException {
        log.info("Processing document: {}", file.getOriginalFilename());
        
        String documentId = UUID.randomUUID().toString();
        String documentName = file.getOriginalFilename();
        
        // Extract text from document
        List<Document> documents = extractTextFromFile(file);
        
        // Split into chunks
        List<Document> chunks = splitIntoChunks(documents);
        
        // Create document chunks with embeddings
        List<DocumentChunk> documentChunks = new ArrayList<>();
        
        for (int i = 0; i < chunks.size(); i++) {
            Document chunk = chunks.get(i);
            String content = chunk.getContent(); // Spring AI Document的内容获取方法

            // Generate embedding for chunk
            List<Float> embedding = embeddingService.generateEmbedding(content);

            DocumentChunk documentChunk = DocumentChunk.builder()
                    .id(UUID.randomUUID().toString())
                    .documentId(documentId)
                    .documentName(documentName)
                    .content(content)
                    .embedding(embedding)
                    .chunkIndex(i)
                    .createdAt(LocalDateTime.now())
                    .metadata(chunk.getMetadata() != null ? chunk.getMetadata().toString() : "")
                    .build();

            documentChunks.add(documentChunk);
        }
        
        log.info("Created {} chunks for document: {}", documentChunks.size(), documentName);
        return documentChunks;
    }

    private List<Document> extractTextFromFile(MultipartFile file) throws IOException {
        String filename = file.getOriginalFilename();
        String contentType = file.getContentType();
        
        log.debug("Extracting text from file: {} (type: {})", filename, contentType);
        
        Resource resource = file.getResource();
        
        if (filename != null && filename.toLowerCase().endsWith(".pdf")) {
            // Use PDF reader for PDF files with default config
            PagePdfDocumentReader pdfReader = new PagePdfDocumentReader(resource);
            return pdfReader.get();
        } else {
            // Use Tika reader for other file types
            TikaDocumentReader tikaReader = new TikaDocumentReader(resource);
            return tikaReader.get();
        }
    }

    private List<Document> splitIntoChunks(List<Document> documents) {
        log.debug("Splitting documents into chunks (size: {}, overlap: {})", 
                ragProperties.getChunk().getSize(), 
                ragProperties.getChunk().getOverlap());
        
        TokenTextSplitter textSplitter = new TokenTextSplitter(
                ragProperties.getChunk().getSize(),
                ragProperties.getChunk().getOverlap(),
                5,  // minimum chunk size
                10000,  // maximum chunk size
                true  // keep separator
        );
        
        return textSplitter.apply(documents);
    }
}
