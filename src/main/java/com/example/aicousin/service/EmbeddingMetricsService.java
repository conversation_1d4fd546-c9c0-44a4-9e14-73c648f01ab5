package com.example.aicousin.service;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * Embedding性能指标收集服务
 */
@Slf4j
@Service
public class EmbeddingMetricsService {
    
    // 计数器
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong externalServiceRequests = new AtomicLong(0);
    private final AtomicLong deepSeekRequests = new AtomicLong(0);
    private final AtomicLong fallbackRequests = new AtomicLong(0);
    private final AtomicLong failedRequests = new AtomicLong(0);
    
    // 性能指标
    private final LongAdder totalProcessingTime = new LongAdder();
    private final AtomicLong maxProcessingTime = new AtomicLong(0);
    private final AtomicLong minProcessingTime = new AtomicLong(Long.MAX_VALUE);
    
    // 文本统计
    private final LongAdder totalTextsProcessed = new LongAdder();
    private final AtomicLong maxBatchSize = new AtomicLong(0);
    
    // 启动时间
    private final LocalDateTime startTime = LocalDateTime.now();
    
    /**
     * 记录请求开始
     */
    public RequestMetrics startRequest(String source, int textCount) {
        totalRequests.incrementAndGet();
        totalTextsProcessed.add(textCount);
        
        // 更新最大批次大小
        maxBatchSize.updateAndGet(current -> Math.max(current, textCount));
        
        // 根据来源更新计数器
        switch (source.toLowerCase()) {
            case "external":
                externalServiceRequests.incrementAndGet();
                break;
            case "deepseek":
                deepSeekRequests.incrementAndGet();
                break;
            case "fallback":
                fallbackRequests.incrementAndGet();
                break;
        }
        
        return new RequestMetrics(System.currentTimeMillis(), source, textCount);
    }
    
    /**
     * 记录请求完成
     */
    public void endRequest(RequestMetrics metrics, boolean success) {
        long processingTime = System.currentTimeMillis() - metrics.getStartTime();
        
        if (success) {
            // 更新性能指标
            totalProcessingTime.add(processingTime);
            maxProcessingTime.updateAndGet(current -> Math.max(current, processingTime));
            minProcessingTime.updateAndGet(current -> Math.min(current, processingTime));
            
            log.debug("Request completed: source={}, texts={}, time={}ms", 
                metrics.getSource(), metrics.getTextCount(), processingTime);
        } else {
            failedRequests.incrementAndGet();
            log.warn("Request failed: source={}, texts={}, time={}ms", 
                metrics.getSource(), metrics.getTextCount(), processingTime);
        }
    }
    
    /**
     * 获取性能统计
     */
    public PerformanceStats getPerformanceStats() {
        long total = totalRequests.get();
        long totalTime = totalProcessingTime.sum();
        long totalTexts = totalTextsProcessed.sum();
        
        double avgProcessingTime = total > 0 ? (double) totalTime / total : 0;
        double avgTextsPerRequest = total > 0 ? (double) totalTexts / total : 0;
        double avgTimePerText = totalTexts > 0 ? (double) totalTime / totalTexts : 0;
        
        long min = minProcessingTime.get();
        if (min == Long.MAX_VALUE) min = 0;
        
        return PerformanceStats.builder()
            .startTime(startTime)
            .totalRequests(total)
            .externalServiceRequests(externalServiceRequests.get())
            .deepSeekRequests(deepSeekRequests.get())
            .fallbackRequests(fallbackRequests.get())
            .failedRequests(failedRequests.get())
            .successRate(total > 0 ? (double) (total - failedRequests.get()) / total : 0)
            .totalTextsProcessed(totalTexts)
            .maxBatchSize(maxBatchSize.get())
            .avgTextsPerRequest(avgTextsPerRequest)
            .totalProcessingTimeMs(totalTime)
            .avgProcessingTimeMs(avgProcessingTime)
            .minProcessingTimeMs(min)
            .maxProcessingTimeMs(maxProcessingTime.get())
            .avgTimePerTextMs(avgTimePerText)
            .build();
    }
    
    /**
     * 重置所有指标
     */
    public void resetMetrics() {
        totalRequests.set(0);
        externalServiceRequests.set(0);
        deepSeekRequests.set(0);
        fallbackRequests.set(0);
        failedRequests.set(0);
        
        totalProcessingTime.reset();
        maxProcessingTime.set(0);
        minProcessingTime.set(Long.MAX_VALUE);
        
        totalTextsProcessed.reset();
        maxBatchSize.set(0);
        
        log.info("Embedding metrics reset");
    }
    
    /**
     * 请求指标
     */
    @Data
    public static class RequestMetrics {
        private final long startTime;
        private final String source;
        private final int textCount;
    }
    
    /**
     * 性能统计
     */
    @Data
    @lombok.Builder
    public static class PerformanceStats {
        private final LocalDateTime startTime;
        
        // 请求统计
        private final long totalRequests;
        private final long externalServiceRequests;
        private final long deepSeekRequests;
        private final long fallbackRequests;
        private final long failedRequests;
        private final double successRate;
        
        // 文本统计
        private final long totalTextsProcessed;
        private final long maxBatchSize;
        private final double avgTextsPerRequest;
        
        // 性能统计
        private final long totalProcessingTimeMs;
        private final double avgProcessingTimeMs;
        private final long minProcessingTimeMs;
        private final long maxProcessingTimeMs;
        private final double avgTimePerTextMs;
        
        /**
         * 获取格式化的统计报告
         */
        public String getFormattedReport() {
            StringBuilder report = new StringBuilder();
            report.append("=== Embedding Performance Report ===\n");
            report.append(String.format("Service Uptime: %s\n", 
                java.time.Duration.between(startTime, LocalDateTime.now())));
            report.append(String.format("Total Requests: %,d (Success Rate: %.1f%%)\n", 
                totalRequests, successRate * 100));
            report.append(String.format("  - External Service: %,d\n", externalServiceRequests));
            report.append(String.format("  - DeepSeek: %,d\n", deepSeekRequests));
            report.append(String.format("  - Fallback: %,d\n", fallbackRequests));
            report.append(String.format("  - Failed: %,d\n", failedRequests));
            report.append(String.format("Total Texts Processed: %,d\n", totalTextsProcessed));
            report.append(String.format("Max Batch Size: %,d\n", maxBatchSize));
            report.append(String.format("Avg Texts per Request: %.1f\n", avgTextsPerRequest));
            report.append(String.format("Processing Time - Avg: %.1fms, Min: %dms, Max: %dms\n", 
                avgProcessingTimeMs, minProcessingTimeMs, maxProcessingTimeMs));
            report.append(String.format("Avg Time per Text: %.1fms\n", avgTimePerTextMs));
            return report.toString();
        }
    }
}
