package com.example.aicousin.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Embedding缓存服务
 * 提供内存缓存来避免重复计算相同文本的向量
 */
@Slf4j
@Service
public class EmbeddingCacheService {
    
    private final ConcurrentHashMap<String, List<Float>> embeddingCache = new ConcurrentHashMap<>();
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);
    
    // 缓存配置
    private static final int MAX_CACHE_SIZE = 10000; // 最大缓存条目数
    private static final int MAX_TEXT_LENGTH = 2000; // 只缓存较短的文本
    
    /**
     * 获取缓存的embedding
     */
    public List<Float> getCachedEmbedding(String text) {
        if (text == null || text.length() > MAX_TEXT_LENGTH) {
            return null;
        }
        
        String key = generateCacheKey(text);
        List<Float> cached = embeddingCache.get(key);
        
        if (cached != null) {
            cacheHits.incrementAndGet();
            log.debug("Cache hit for text hash: {}", key.substring(0, 8));
            return cached;
        } else {
            cacheMisses.incrementAndGet();
            return null;
        }
    }
    
    /**
     * 缓存embedding结果
     */
    public void cacheEmbedding(String text, List<Float> embedding) {
        if (text == null || embedding == null || text.length() > MAX_TEXT_LENGTH) {
            return;
        }
        
        // 检查缓存大小限制
        if (embeddingCache.size() >= MAX_CACHE_SIZE) {
            // 简单的LRU策略：清理一部分缓存
            clearOldestEntries();
        }
        
        String key = generateCacheKey(text);
        embeddingCache.put(key, embedding);
        log.debug("Cached embedding for text hash: {}", key.substring(0, 8));
    }
    
    /**
     * 生成缓存键
     */
    private String generateCacheKey(String text) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(text.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            // 降级到简单hash
            return String.valueOf(text.hashCode());
        }
    }
    
    /**
     * 清理最旧的缓存条目
     */
    private void clearOldestEntries() {
        int entriesToRemove = MAX_CACHE_SIZE / 4; // 清理25%的条目
        embeddingCache.entrySet().stream()
                .limit(entriesToRemove)
                .forEach(entry -> embeddingCache.remove(entry.getKey()));
        
        log.info("Cleared {} cache entries, current size: {}", entriesToRemove, embeddingCache.size());
    }
    
    /**
     * 获取缓存统计信息
     */
    public CacheStats getCacheStats() {
        long hits = cacheHits.get();
        long misses = cacheMisses.get();
        long total = hits + misses;
        double hitRate = total > 0 ? (double) hits / total : 0.0;
        
        return new CacheStats(hits, misses, hitRate, embeddingCache.size());
    }
    
    /**
     * 清空缓存
     */
    public void clearCache() {
        embeddingCache.clear();
        cacheHits.set(0);
        cacheMisses.set(0);
        log.info("Embedding cache cleared");
    }
    
    /**
     * 缓存统计信息
     */
    public static class CacheStats {
        private final long hits;
        private final long misses;
        private final double hitRate;
        private final int size;
        
        public CacheStats(long hits, long misses, double hitRate, int size) {
            this.hits = hits;
            this.misses = misses;
            this.hitRate = hitRate;
            this.size = size;
        }
        
        public long getHits() { return hits; }
        public long getMisses() { return misses; }
        public double getHitRate() { return hitRate; }
        public int getSize() { return size; }
        
        @Override
        public String toString() {
            return String.format("CacheStats{hits=%d, misses=%d, hitRate=%.2f%%, size=%d}", 
                hits, misses, hitRate * 100, size);
        }
    }
}
