package com.example.aicousin.service;

import com.example.aicousin.dto.QueryResponse;
import com.example.aicousin.dto.TableSchemaInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class GenerationService {

    private final ChatModel chatModel;

    @jakarta.annotation.PostConstruct
    public void checkChatApi() {
        log.info("🤖 Chat API 信息:");
        log.info("  - ChatModel: {} ✅", chatModel.getClass().getSimpleName());
        log.info("  - ChatClient: 在Spring AI 1.0.0-M4中不可用 ❌");
        log.info("💡 建议: 使用ChatModel进行聊天功能，这是当前版本的标准API");
    }

    private static final String SYSTEM_PROMPT = """
            你是一个专业的AI助手，擅长根据提供的上下文回答用户问题，并能够基于数据库Schema生成SQL查询。

            回答指导原则：
            1. 严格基于提供的上下文内容回答问题，不要编造信息。
            2. 如果上下文信息不足以回答问题，请明确说明。
            3. 回答要准确、全面且条理清晰。
            4. **如果用户问题涉及数据库查询且提供了数据库Schema上下文，请尝试生成相应的SQL查询。**
               - SQL查询应使用Markdown代码块（```sql...```）进行格式化。
               - SQL查询后，请简要解释该SQL的作用。
               - **只有当问题明确要求数据查询时才生成SQL。**
            5. 引用文档信息时，请标注来源文档。
            6. 保持专业、友好的语调。
            7. 优先使用中文回答，除非用户明确要求其他语言。
            8. 对多个文档片段或Schema信息进行整合和总结。
            9. 如果发现信息冲突，请指出并说明不同来源的观点。

            上下文将以编号段落的形式提供，包含文档内容和数据库Schema信息。
            """;

    /**
     * Generate answer based on query and retrieved chunks
     */
    public String generateAnswer(String query, List<QueryResponse.RetrievedChunk> retrievedChunks) {
        log.info("🤖 开始生成回答，查询: '{}', 检索到 {} 个相关片段", query, retrievedChunks.size());

        if (retrievedChunks.isEmpty()) {
            log.warn("⚠️ 没有检索到相关片段");
            return "抱歉，我没有找到与您的问题相关的信息。请尝试：\n" +
                   "1. 重新表述您的问题\n" +
                   "2. 检查是否已上传相关文档或配置数据库Schema\n" +
                   "3. 降低相似度阈值以获取更多结果";
        }

        // 分析和预处理检索结果
        ContextAnalysis contextAnalysis = analyzeContext(retrievedChunks);
        log.info("📊 上下文分析: 文档片段: {}, Schema片段: {}, 平均相似度: {:.3f}",
                contextAnalysis.documentChunkCount, contextAnalysis.schemaChunkCount, contextAnalysis.averageSimilarity);

        // 构建增强的上下文
        String enhancedContext = buildEnhancedContext(retrievedChunks, contextAnalysis);

        // 创建针对性的用户提示词
        String userPrompt = buildUserPrompt(query, enhancedContext, contextAnalysis);

        List<Message> messages = List.of(
                new SystemMessage(SYSTEM_PROMPT),
                new UserMessage(userPrompt)
        );

        try {
            log.info("🚀 调用DeepSeek生成回答...");
            Prompt prompt = new Prompt(messages);
            ChatResponse response = chatModel.call(prompt);

            String answer = response.getResult().getOutput().getContent();
            log.info("✅ 成功生成回答，长度: {} 字符", answer.length());

            // 后处理回答
            return postProcessAnswer(answer, contextAnalysis);

        } catch (Exception e) {
            log.error("❌ 生成回答时出错", e);
            return "抱歉，在生成回答时遇到了技术问题。请稍后重试。\n" +
                   "错误信息：" + e.getMessage();
        }
    }

    /**
     * 上下文分析结果
     */
    private static class ContextAnalysis {
        final int documentChunkCount;
        final int schemaChunkCount;
        final double averageSimilarity;
        final double maxSimilarity;
        final double minSimilarity;
        final Set<String> documentNames;
        final Set<String> databaseTableNames; // New: Stores "db.table" names
        final boolean hasHighQualityMatches;
        final int totalContentLength;

        ContextAnalysis(int documentChunkCount, int schemaChunkCount, double averageSimilarity, double maxSimilarity,
                       double minSimilarity, Set<String> documentNames, Set<String> databaseTableNames,
                       boolean hasHighQualityMatches, int totalContentLength) {
            this.documentChunkCount = documentChunkCount;
            this.schemaChunkCount = schemaChunkCount;
            this.averageSimilarity = averageSimilarity;
            this.maxSimilarity = maxSimilarity;
            this.minSimilarity = minSimilarity;
            this.documentNames = documentNames;
            this.databaseTableNames = databaseTableNames;
            this.hasHighQualityMatches = hasHighQualityMatches;
            this.totalContentLength = totalContentLength;
        }
    }

    /**
     * 分析检索到的上下文
     */
    private ContextAnalysis analyzeContext(List<QueryResponse.RetrievedChunk> chunks) {
        if (chunks.isEmpty()) {
            return new ContextAnalysis(0, 0, 0.0, 0.0, 0.0, Set.of(), Set.of(), false, 0);
        }

        Set<String> documentNames = chunks.stream()
                .filter(chunk -> "document".equals(chunk.getSourceType()))
                .map(QueryResponse.RetrievedChunk::getDocumentName)
                .collect(Collectors.toSet());

        Set<String> databaseTableNames = chunks.stream()
                .filter(chunk -> "db_schema".equals(chunk.getSourceType()))
                .map(chunk -> chunk.getDbName() + "." + chunk.getTableName())
                .collect(Collectors.toSet());

        int documentChunkCount = (int) chunks.stream()
                .filter(chunk -> "document".equals(chunk.getSourceType()))
                .count();

        int schemaChunkCount = (int) chunks.stream()
                .filter(chunk -> "db_schema".equals(chunk.getSourceType()))
                .count();

        double averageSimilarity = chunks.stream()
                .mapToDouble(QueryResponse.RetrievedChunk::getSimilarity)
                .average()
                .orElse(0.0);

        double maxSimilarity = chunks.stream()
                .mapToDouble(QueryResponse.RetrievedChunk::getSimilarity)
                .max()
                .orElse(0.0);

        double minSimilarity = chunks.stream()
                .mapToDouble(QueryResponse.RetrievedChunk::getSimilarity)
                .min()
                .orElse(0.0);

        boolean hasHighQualityMatches = maxSimilarity > 0.8;

        int totalContentLength = chunks.stream()
                .mapToInt(chunk -> chunk.getContent().length())
                .sum();

        return new ContextAnalysis(documentChunkCount, schemaChunkCount, averageSimilarity, maxSimilarity,
                                 minSimilarity, documentNames, databaseTableNames, hasHighQualityMatches, totalContentLength);
    }

    /**
     * 构建增强的上下文信息
     */
    private String buildEnhancedContext(List<QueryResponse.RetrievedChunk> chunks, ContextAnalysis analysis) {
        StringBuilder contextBuilder = new StringBuilder();

        // 添加上下文概览
        contextBuilder.append("=== 检索结果概览 ===\n");
        contextBuilder.append(String.format("共找到 %d 个相关片段 (文档: %d, Schema: %d)\n",
                chunks.size(), analysis.documentChunkCount, analysis.schemaChunkCount));
        contextBuilder.append(String.format("相似度范围：%.3f - %.3f (平均：%.3f)\n\n",
                analysis.minSimilarity, analysis.maxSimilarity, analysis.averageSimilarity));

        // 按文档分组显示
        Map<String, List<QueryResponse.RetrievedChunk>> documentChunksByDocument = chunks.stream()
                .filter(chunk -> "document".equals(chunk.getSourceType()))
                .collect(Collectors.groupingBy(QueryResponse.RetrievedChunk::getDocumentName));

        // 按数据库表分组显示Schema
        Map<String, List<QueryResponse.RetrievedChunk>> schemaChunksByTable = chunks.stream()
                .filter(chunk -> "db_schema".equals(chunk.getSourceType()))
                .collect(Collectors.groupingBy(chunk -> chunk.getDbName() + "." + chunk.getTableName()));

        int sectionNumber = 1;
        // Add Document sections
        if (!documentChunksByDocument.isEmpty()) {
            for (Map.Entry<String, List<QueryResponse.RetrievedChunk>> entry : documentChunksByDocument.entrySet()) {
                String documentName = entry.getKey();
                List<QueryResponse.RetrievedChunk> documentChunks = entry.getValue();

                contextBuilder.append(String.format("=== 文档 %d: %s ===\n", sectionNumber++, documentName));

                // 按相似度排序
                documentChunks.sort((a, b) -> Double.compare(b.getSimilarity(), a.getSimilarity()));

                for (int i = 0; i < documentChunks.size(); i++) {
                    QueryResponse.RetrievedChunk chunk = documentChunks.get(i);
                    contextBuilder.append(String.format("\n【片段 %d.%d】(相似度: %.3f, 块索引: %d)\n",
                            sectionNumber - 1, i + 1, chunk.getSimilarity(), chunk.getChunkIndex()));
                    contextBuilder.append(chunk.getContent());
                    contextBuilder.append("\n");
                }
                contextBuilder.append("\n");
            }
        }

        // Add Database Schema sections
        if (!schemaChunksByTable.isEmpty()) {
            contextBuilder.append("=== 数据库Schema信息 ===\n\n");
            for (Map.Entry<String, List<QueryResponse.RetrievedChunk>> entry : schemaChunksByTable.entrySet()) {
                String dbTableName = entry.getKey();
                List<QueryResponse.RetrievedChunk> schemaChunks = entry.getValue();

                contextBuilder.append(String.format("--- 表: %s ---\n", dbTableName));

                // Schema chunks usually contain the full schema in one chunk, but sort by similarity anyway
                schemaChunks.sort((a, b) -> Double.compare(b.getSimilarity(), a.getSimilarity()));

                for (QueryResponse.RetrievedChunk chunk : schemaChunks) {
                    contextBuilder.append("```sql\n"); // Format schema text as SQL for clarity to LLM
                    contextBuilder.append(chunk.getContent());
                    contextBuilder.append("\n```\n");
                    contextBuilder.append(String.format("(相似度: %.3f)\n\n", chunk.getSimilarity()));
                }
            }
        }

        return contextBuilder.toString();
    }

    /**
     * 构建用户提示词
     */
    private String buildUserPrompt(String query, String context, ContextAnalysis analysis) {
        StringBuilder promptBuilder = new StringBuilder();

        promptBuilder.append(String.format("用户问题：%s\n\n", query));

        // 根据上下文质量调整提示
        if (analysis.hasHighQualityMatches) {
            promptBuilder.append("以下是高度相关的上下文内容，请基于这些信息提供准确、详细的回答：\n\n");
        } else if (analysis.averageSimilarity > 0.6) {
            promptBuilder.append("以下是相关的上下文内容，请基于这些信息回答问题，如果信息不够完整请说明：\n\n");
        } else {
            promptBuilder.append("以下是可能相关的上下文内容，相似度较低，请谨慎回答并说明信息的局限性：\n\n");
        }

        promptBuilder.append(context);

        promptBuilder.append("\n请根据以上上下文信息回答用户问题。要求：\n");
        promptBuilder.append("1. 如果信息充分，请提供详细、准确的回答\n");
        promptBuilder.append("2. 如果信息不足，请明确说明并建议用户如何获取更多信息\n");

        // Add specific instruction for SQL generation if schema is present and query is about data
        if (analysis.schemaChunkCount > 0 && isSqlQueryIntent(query)) {
            promptBuilder.append("3. 如果问题涉及数据库查询，请根据提供的Schema信息生成SQL查询，并用```sql...```代码块表示，然后简要解释SQL的作用。\n");
        } else {
            promptBuilder.append("3. 引用信息时请标注来源（文档名称或数据库表名）。\n");
        }

        promptBuilder.append("4. 如果多个来源有相关信息，请进行整合总结\n");
        promptBuilder.append("5. 保持回答的逻辑性和可读性\n");

        return promptBuilder.toString();
    }

    /**
     * 后处理生成的回答
     */
    private String postProcessAnswer(String answer, ContextAnalysis analysis) {
        StringBuilder processedAnswer = new StringBuilder(answer);

        // 如果相似度较低，添加置信度说明
        if (analysis.averageSimilarity < 0.7) {
            processedAnswer.append("\n\n💡 **说明**：此回答基于相似度较低的检索结果生成，建议您：");
            processedAnswer.append("\n- 尝试使用不同的关键词重新提问");
            if (analysis.documentChunkCount > 0) {
                processedAnswer.append("\n- 上传更多相关文档以获得更准确的答案");
            }
            if (analysis.schemaChunkCount > 0) {
                processedAnswer.append("\n- 确认数据库Schema是否已正确配置和导入");
            }
        }

        // 添加数据来源说明
        if (!analysis.documentNames.isEmpty() || !analysis.databaseTableNames.isEmpty()) {
            processedAnswer.append("\n\n📚 **信息来源**：");
            if (!analysis.documentNames.isEmpty()) {
                processedAnswer.append("\n- 文档: ").append(String.join(", ", analysis.documentNames));
            }
            if (!analysis.databaseTableNames.isEmpty()) {
                processedAnswer.append("\n- 数据库Schema: ").append(String.join(", ", analysis.databaseTableNames));
            }
        }

        return processedAnswer.toString();
    }

    /**
     * 简单判断用户意图是否倾向于SQL查询
     * 可以根据实际需求增加更复杂的NLP模型或关键词匹配规则
     */
    private boolean isSqlQueryIntent(String query) {
        String lowerCaseQuery = query.toLowerCase();
        return lowerCaseQuery.contains("查询") ||
               lowerCaseQuery.contains("统计") ||
               lowerCaseQuery.contains("列出") ||
               lowerCaseQuery.contains("找出") ||
               lowerCaseQuery.contains("多少") ||
               lowerCaseQuery.contains("什么数据") ||
               lowerCaseQuery.contains("sql");
    }

    /**
     * Generate a direct answer using only the LLM, without RAG context.
     */
    public String generateDirectAnswer(String query) {
        log.info("🤖 直接调用DeepSeek生成回答 (无RAG上下文)，查询: '{}'", query);
        List<Message> messages = List.of(new UserMessage(query));
        Prompt prompt = new Prompt(messages);
        try {
            ChatResponse response = chatModel.call(prompt);
            return response.getResult().getOutput().getContent();
        } catch (Exception e) {
            log.error("❌ 直接生成回答时出错", e);
            return "抱歉，在直接生成回答时遇到了技术问题。请稍后重试。";
        }
    }
}
