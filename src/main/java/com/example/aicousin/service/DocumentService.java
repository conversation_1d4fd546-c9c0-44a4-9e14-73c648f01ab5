package com.example.aicousin.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.context.annotation.DependsOn;
import jakarta.annotation.PostConstruct;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 文档管理服务
 */
@Slf4j
@Service
@DependsOn("milvusVectorService")
public class DocumentService {

    // 内存存储文档信息（生产环境应该使用数据库）
    private final Map<String, DocumentInfo> documents = new ConcurrentHashMap<>();
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private MilvusVectorService milvusVectorService;

    /**
     * 文档信息类
     */
    public static class DocumentInfo {
        private String id;
        private String name;
        private String originalName;
        private long size;
        private String sizeFormatted;
        private LocalDateTime uploadTime;
        private String uploadTimeFormatted;
        private int chunks;
        private String status;
        private String filePath;
        private String contentType;

        // 构造函数
        public DocumentInfo(String id, String name, String originalName, long size, String filePath, String contentType) {
            this.id = id;
            this.name = name;
            this.originalName = originalName;
            this.size = size;
            this.sizeFormatted = formatFileSize(size);
            this.uploadTime = LocalDateTime.now();
            this.uploadTimeFormatted = this.uploadTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            this.chunks = 0;
            this.status = "处理中";
            this.filePath = filePath;
            this.contentType = contentType;
        }

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getOriginalName() { return originalName; }
        public void setOriginalName(String originalName) { this.originalName = originalName; }

        public long getSize() { return size; }
        public void setSize(long size) { 
            this.size = size; 
            this.sizeFormatted = formatFileSize(size);
        }

        public String getSizeFormatted() { return sizeFormatted; }

        public LocalDateTime getUploadTime() { return uploadTime; }
        public void setUploadTime(LocalDateTime uploadTime) { 
            this.uploadTime = uploadTime; 
            this.uploadTimeFormatted = uploadTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        public String getUploadTimeFormatted() { return uploadTimeFormatted; }

        public int getChunks() { return chunks; }
        public void setChunks(int chunks) { this.chunks = chunks; }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }

        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }

        public String getContentType() { return contentType; }
        public void setContentType(String contentType) { this.contentType = contentType; }

        private static String formatFileSize(long size) {
            if (size < 1024) return size + " B";
            if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
            if (size < 1024 * 1024 * 1024) return String.format("%.1f MB", size / (1024.0 * 1024.0));
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }

        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>();
            map.put("id", id);
            map.put("name", name);
            map.put("originalName", originalName);
            map.put("size", sizeFormatted);
            map.put("uploadTime", uploadTimeFormatted);
            map.put("chunks", chunks);
            map.put("status", status);
            map.put("contentType", contentType);
            return map;
        }
    }

    /**
     * 添加文档
     */
    public DocumentInfo addDocument(String name, String originalName, long size, String filePath, String contentType) {
        String id = generateDocumentId();
        DocumentInfo doc = new DocumentInfo(id, name, originalName, size, filePath, contentType);
        documents.put(id, doc);
        
        log.info("📄 添加文档: {} (ID: {})", name, id);
        return doc;
    }

    /**
     * 获取所有文档
     */
    public List<DocumentInfo> getAllDocuments() {
        List<DocumentInfo> docList = new ArrayList<>(documents.values());
        // 按上传时间倒序排列
        docList.sort((a, b) -> b.getUploadTime().compareTo(a.getUploadTime()));
        return docList;
    }

    /**
     * 根据ID获取文档
     */
    public DocumentInfo getDocument(String id) {
        return documents.get(id);
    }

    /**
     * 删除文档
     */
    public boolean deleteDocument(String id) {
        DocumentInfo doc = documents.remove(id);
        if (doc != null) {
            log.info("🗑️ 删除文档: {} (ID: {})", doc.getName(), id);
            return true;
        }
        return false;
    }

    /**
     * 更新文档状态
     */
    public void updateDocumentStatus(String id, String status) {
        DocumentInfo doc = documents.get(id);
        if (doc != null) {
            doc.setStatus(status);
            log.info("📊 更新文档状态: {} -> {}", doc.getName(), status);
        }
    }

    /**
     * 更新文档片段数
     */
    public void updateDocumentChunks(String id, int chunks) {
        DocumentInfo doc = documents.get(id);
        if (doc != null) {
            doc.setChunks(chunks);
            log.info("📊 更新文档片段数: {} -> {} chunks", doc.getName(), chunks);
        }
    }

    /**
     * 获取文档总数
     */
    public int getDocumentCount() {
        return documents.size();
    }

    /**
     * 检查文档是否存在
     */
    public boolean documentExists(String id) {
        return documents.containsKey(id);
    }

    /**
     * 生成文档ID
     */
    private String generateDocumentId() {
        return "doc_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 获取文档统计信息
     */
    public Map<String, Object> getDocumentStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("total", documents.size());

        long totalSize = documents.values().stream()
                .mapToLong(DocumentInfo::getSize)
                .sum();
        stats.put("totalSize", DocumentInfo.formatFileSize(totalSize));

        long totalChunks = documents.values().stream()
                .mapToInt(DocumentInfo::getChunks)
                .sum();
        stats.put("totalChunks", totalChunks);

        Map<String, Long> statusCount = new HashMap<>();
        documents.values().forEach(doc -> {
            statusCount.merge(doc.getStatus(), 1L, Long::sum);
        });
        stats.put("statusCount", statusCount);

        return stats;
    }

    /**
     * 清空内存中的文档数据（仅用于测试恢复功能）
     */
    public void clearDocuments() {
        int count = documents.size();
        documents.clear();
        log.info("🗑️ 已清空内存中的 {} 个文档", count);
    }

    /**
     * 应用启动时从Milvus恢复文档元数据
     */
    @PostConstruct
    public void recoverDocumentsFromMilvus() {
        log.info("🔄 正在从Milvus恢复文档元数据...");

        // 如果内存中已有文档，则不需要恢复
        if (!documents.isEmpty()) {
            log.info("✅ 内存中已有 {} 个文档，无需从Milvus恢复", documents.size());
            return;
        }

        try {
            // 从Milvus获取所有文档元数据
            List<Map<String, Object>> docMetadataList = milvusVectorService.getAllDocumentMetadata();

            if (docMetadataList.isEmpty()) {
                log.info("⚠️ Milvus中没有找到文档元数据");
                return;
            }

            int recoveredCount = 0;

            // 恢复每个文档的元数据
            for (Map<String, Object> metadata : docMetadataList) {
                String documentId = (String) metadata.get("documentId");
                String documentName = (String) metadata.get("documentName");
                String createdAtStr = (String) metadata.get("createdAt");
                int chunksCount = (Integer) metadata.get("chunksCount");

                // 解析创建时间
                LocalDateTime createdAt = LocalDateTime.parse(createdAtStr);

                // 创建文档信息对象
                DocumentInfo docInfo = new DocumentInfo(
                    documentId,
                    documentName,
                    documentName,  // originalName
                    0,  // 文件大小未知，设为0
                    null,  // filePath未知
                    null   // contentType未知
                );

                // 设置上传时间和分块数
                docInfo.setUploadTime(createdAt);
                docInfo.setChunks(chunksCount);
                docInfo.setStatus("已处理");  // 设置为已处理状态

                // 添加到内存映射
                documents.put(documentId, docInfo);
                recoveredCount++;
            }

            log.info("✅ 成功从Milvus恢复了 {} 个文档的元数据", recoveredCount);

        } catch (Exception e) {
            log.error("❌ 从Milvus恢复文档元数据失败: {}", e.getMessage(), e);
        }
    }
}
