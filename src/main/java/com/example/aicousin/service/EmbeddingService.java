package com.example.aicousin.service;

import com.example.aicousin.config.ExternalEmbeddingProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.EmbeddingResponse;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmbeddingService {

    private final EmbeddingModel embeddingModel;
    private final ExternalEmbeddingClient externalEmbeddingClient;
    private final ExternalEmbeddingProperties externalEmbeddingProperties;
    private final EmbeddingCacheService cacheService;
    private final EmbeddingMetricsService metricsService;

    private volatile boolean embeddingModelAvailable = false; // DeepSeek模型状态
    private volatile boolean externalEmbeddingAvailable = false; // 外部服务状态
    private volatile String embeddingStatus = "INITIALIZING";

    @jakarta.annotation.PostConstruct
    public void initializeEmbeddingService() {
        log.info("🚀 Initializing embedding services...");

        // 测试外部向量化服务
        testExternalEmbeddingService();

        // 测试DeepSeek embedding模型
        testEmbeddingModel();

        // 更新最终状态
        updateEmbeddingStatus();

        log.info("✅ Embedding service initialization completed. Status: {}", embeddingStatus);
    }

    private void testExternalEmbeddingService() {
        if (externalEmbeddingProperties.isEnabled()) {
            try {
                externalEmbeddingAvailable = externalEmbeddingClient.testConnection();
                if (externalEmbeddingAvailable) {
                    log.info("✅ External embedding service is available at: {}",
                        externalEmbeddingProperties.getFullUrl());
                }
            } catch (Exception e) {
                log.warn("❌ External embedding service test failed: {}", e.getMessage());
                externalEmbeddingAvailable = false;
            }
        } else {
            log.info("ℹ️ External embedding service is disabled");
            externalEmbeddingAvailable = false;
        }
    }

    private void testEmbeddingModel() {
        try {
            log.info("🔍 Testing DeepSeek embedding model...");
            EmbeddingResponse response = embeddingModel.embedForResponse(List.of("test"));
            if (response != null && !response.getResults().isEmpty()) {
                embeddingModelAvailable = true;
                log.info("✅ DeepSeek embedding model is available");
            } else {
                log.warn("⚠️ DeepSeek embedding model returned empty response");
                embeddingModelAvailable = false;
            }
        } catch (Exception e) {
            log.warn("❌ DeepSeek embedding model not available: {}", e.getMessage());
            embeddingModelAvailable = false;
        }
    }

    private void updateEmbeddingStatus() {
        if (externalEmbeddingAvailable) {
            embeddingStatus = "EXTERNAL_SERVICE_AVAILABLE";
        } else if (embeddingModelAvailable) {
            embeddingStatus = "DEEPSEEK_AVAILABLE";
        } else {
            embeddingStatus = "MILVUS_FALLBACK_ONLY";
        }
    }

    /**
     * Generate embedding for a single text with caching and metrics
     * 优先级: 缓存 > 外部服务 > DeepSeek > Milvus Fallback
     */
    public List<Float> generateEmbedding(String text) {
        if (text == null || text.trim().isEmpty()) {
            throw new IllegalArgumentException("Text cannot be null or empty");
        }

        log.debug("Generating embedding for text of length: {}", text.length());

        // 0. 首先检查缓存
        List<Float> cachedResult = cacheService.getCachedEmbedding(text);
        if (cachedResult != null) {
            log.debug("✅ Retrieved embedding from cache");
            return cachedResult;
        }

        EmbeddingMetricsService.RequestMetrics metrics = null;
        String source = null;
        boolean success = false;

        try {
            // 1. 优先尝试外部向量化服务
            if (externalEmbeddingAvailable) {
                source = "external";
                metrics = metricsService.startRequest(source, 1);

                try {
                    List<Float> result = externalEmbeddingClient.generateEmbedding(text);
                    if (result != null && !result.isEmpty()) {
                        log.debug("✅ Generated embedding using external service");
                        cacheService.cacheEmbedding(text, result);
                        success = true;
                        return result;
                    }
                } catch (Exception e) {
                    log.warn("❌ External embedding service failed: {}", e.getMessage());
                    externalEmbeddingAvailable = false;
                    updateEmbeddingStatus();
                }
            }

            // 2. 尝试DeepSeek embedding模型
            if (embeddingModelAvailable) {
                if (metrics != null) {
                    metricsService.endRequest(metrics, false); // 外部服务失败
                }

                source = "deepseek";
                metrics = metricsService.startRequest(source, 1);

                try {
                    EmbeddingResponse response = embeddingModel.embedForResponse(List.of(text));

                    if (response.getResults().isEmpty()) {
                        throw new RuntimeException("No embedding generated for text");
                    }

                    float[] embedding = response.getResults().get(0).getOutput();
                    List<Float> result = new ArrayList<>();
                    for (float f : embedding) {
                        result.add(f);
                    }

                    log.debug("✅ Generated embedding using DeepSeek model");
                    cacheService.cacheEmbedding(text, result);
                    success = true;
                    return result;

                } catch (Exception e) {
                    log.warn("❌ DeepSeek embedding model failed: {}", e.getMessage());
                    embeddingModelAvailable = false;
                    updateEmbeddingStatus();
                }
            }

            // 3. 最后使用Milvus fallback
            if (metrics != null) {
                metricsService.endRequest(metrics, false); // 前面的服务失败
            }

            source = "fallback";
            metrics = metricsService.startRequest(source, 1);

            log.debug("Using Milvus fallback embedding");
            List<Float> result = generateMilvusFallbackEmbedding(text);
            cacheService.cacheEmbedding(text, result);
            success = true;
            return result;

        } finally {
            if (metrics != null) {
                metricsService.endRequest(metrics, success);
            }
        }
    }

    /**
     * Generate embeddings for multiple texts with optimized batching
     * 优先级: 外部服务 > DeepSeek > Milvus Fallback
     */
    public List<List<Float>> generateEmbeddings(List<String> texts) {
        if (texts == null || texts.isEmpty()) {
            return new ArrayList<>();
        }

        log.debug("Generating embeddings for {} texts", texts.size());
        long startTime = System.currentTimeMillis();

        try {
            List<List<Float>> result = generateEmbeddingsWithBatching(texts);
            long endTime = System.currentTimeMillis();
            log.debug("✅ Generated {} embeddings in {}ms (avg: {}ms per text)",
                result.size(), endTime - startTime, (endTime - startTime) / (double) texts.size());
            return result;
        } catch (Exception e) {
            log.error("❌ Failed to generate embeddings: {}", e.getMessage());
            // 最后的fallback
            return texts.stream()
                    .map(this::generateMilvusFallbackEmbedding)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 智能批处理生成embeddings
     */
    private List<List<Float>> generateEmbeddingsWithBatching(List<String> texts) {
        // 确定最佳批处理大小
        int optimalBatchSize = determineOptimalBatchSize(texts.size());

        if (texts.size() <= optimalBatchSize) {
            // 小批量直接处理
            return generateEmbeddingsBatch(texts);
        } else {
            // 大批量分批处理
            return generateEmbeddingsInBatches(texts, optimalBatchSize);
        }
    }

    /**
     * 确定最佳批处理大小
     */
    private int determineOptimalBatchSize(int totalSize) {
        if (externalEmbeddingAvailable) {
            return Math.min(externalEmbeddingProperties.getBatchSize(), totalSize);
        } else if (embeddingModelAvailable) {
            // DeepSeek API建议的批处理大小
            return Math.min(20, totalSize);
        } else {
            // Fallback可以处理更大批次
            return Math.min(100, totalSize);
        }
    }

    /**
     * 分批处理大量文本
     */
    private List<List<Float>> generateEmbeddingsInBatches(List<String> texts, int batchSize) {
        List<List<Float>> allResults = new ArrayList<>();

        for (int i = 0; i < texts.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, texts.size());
            List<String> batch = texts.subList(i, endIndex);

            log.debug("Processing batch {}/{} (size: {})",
                (i / batchSize) + 1, (texts.size() + batchSize - 1) / batchSize, batch.size());

            List<List<Float>> batchResults = generateEmbeddingsBatch(batch);
            allResults.addAll(batchResults);

            // 批次间短暂延迟，避免API限流
            if (i + batchSize < texts.size() && (externalEmbeddingAvailable || embeddingModelAvailable)) {
                try {
                    Thread.sleep(100); // 100ms延迟
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrupted during batch processing", e);
                }
            }
        }

        return allResults;
    }

    /**
     * 单批次处理
     */
    private List<List<Float>> generateEmbeddingsBatch(List<String> texts) {
        // 1. 优先尝试外部向量化服务
        if (externalEmbeddingAvailable) {
            try {
                List<List<Float>> result = externalEmbeddingClient.generateEmbeddings(texts);
                if (result != null && result.size() == texts.size()) {
                    log.debug("✅ Generated {} embeddings using external service", result.size());
                    return result;
                }
            } catch (Exception e) {
                log.warn("❌ External embedding service batch failed: {}", e.getMessage());
                externalEmbeddingAvailable = false;
                updateEmbeddingStatus();
            }
        }

        // 2. 尝试DeepSeek embedding模型
        if (embeddingModelAvailable) {
            try {
                EmbeddingResponse response = embeddingModel.embedForResponse(texts);

                List<List<Float>> result = response.getResults().stream()
                        .map(embeddingResult -> {
                            float[] embedding = embeddingResult.getOutput();
                            List<Float> floatList = new ArrayList<>();
                            for (float f : embedding) {
                                floatList.add(f);
                            }
                            return floatList;
                        })
                        .collect(Collectors.toList());

                log.debug("✅ Generated {} embeddings using DeepSeek model", result.size());
                return result;

            } catch (Exception e) {
                log.warn("❌ DeepSeek batch embedding failed: {}", e.getMessage());
                embeddingModelAvailable = false;
                updateEmbeddingStatus();
            }
        }

        // 3. 最后使用Milvus fallback
        log.debug("Using Milvus fallback embedding for {} texts", texts.size());
        return texts.stream()
                .map(this::generateMilvusFallbackEmbedding)
                .collect(Collectors.toList());
    }

    /**
     * Generate fallback embedding using Milvus-compatible approach
     * This creates a simple but consistent vector representation
     */
    private List<Float> generateMilvusFallbackEmbedding(String text) {
        log.debug("Generating Milvus fallback embedding for text of length: {}", text.length());

        // Create a 1536-dimensional vector (same as OpenAI embeddings)
        int dimension = 1536;
        List<Float> embedding = new ArrayList<>(dimension);

        // Use text hash and content features to generate consistent embeddings
        int textHash = text.hashCode();
        Random random = new Random(textHash); // Seed with text hash for consistency

        // Generate base vector using text characteristics
        for (int i = 0; i < dimension; i++) {
            float value = (float) (random.nextGaussian() * 0.1); // Small random values

            // Add text-specific features
            if (i < text.length() && i < 100) {
                char c = text.charAt(i % text.length());
                value += (c / 1000.0f); // Character influence
            }

            // Add word count influence
            if (i % 100 == 0) {
                value += text.split("\\s+").length / 10000.0f;
            }

            embedding.add(value);
        }

        // Normalize the vector
        return normalizeVector(embedding);
    }

    /**
     * Normalize vector to unit length
     */
    private List<Float> normalizeVector(List<Float> vector) {
        double magnitude = Math.sqrt(vector.stream()
                .mapToDouble(f -> f * f)
                .sum());

        if (magnitude == 0) {
            magnitude = 1.0; // Avoid division by zero
        }

        final double finalMagnitude = magnitude;
        return vector.stream()
                .map(f -> (float) (f / finalMagnitude))
                .collect(Collectors.toList());
    }

    /**
     * Get current embedding status
     */
    public String getEmbeddingStatus() {
        return embeddingStatus;
    }

    /**
     * Check if any embedding service is available
     */
    public boolean isEmbeddingModelAvailable() {
        return externalEmbeddingAvailable || embeddingModelAvailable;
    }

    /**
     * Check if external embedding service is available
     */
    public boolean isExternalEmbeddingAvailable() {
        return externalEmbeddingAvailable;
    }

    /**
     * Check if DeepSeek embedding model is available
     */
    public boolean isDeepSeekEmbeddingAvailable() {
        return embeddingModelAvailable;
    }

    /**
     * Force retry of all embedding services
     */
    public void retryEmbeddingServices() {
        log.info("🔄 Retrying all embedding services...");

        // 重新测试外部服务
        testExternalEmbeddingService();

        // 重新测试DeepSeek模型
        testEmbeddingModel();

        // 更新状态
        updateEmbeddingStatus();

        log.info("✅ Embedding services retry completed. Status: {}", embeddingStatus);
    }

    /**
     * Get detailed embedding service information
     */
    public String getDetailedStatus() {
        StringBuilder status = new StringBuilder();
        status.append("Embedding Services Status:\n");
        status.append("- External Service: ").append(externalEmbeddingAvailable ? "✅ Available" : "❌ Unavailable");
        if (externalEmbeddingProperties.isEnabled()) {
            status.append(" (").append(externalEmbeddingProperties.getFullUrl()).append(")");
        } else {
            status.append(" (Disabled)");
        }
        status.append("\n");
        status.append("- DeepSeek Model: ").append(embeddingModelAvailable ? "✅ Available" : "❌ Unavailable").append("\n");
        status.append("- Milvus Fallback: ✅ Always Available\n");
        status.append("- Current Priority: ");
        if (externalEmbeddingAvailable) {
            status.append("External Service");
        } else if (embeddingModelAvailable) {
            status.append("DeepSeek Model");
        } else {
            status.append("Milvus Fallback");
        }
        return status.toString();
    }

    /**
     * 异步生成单个文本的embedding
     */
    @Async("embeddingExecutor")
    public CompletableFuture<List<Float>> generateEmbeddingAsync(String text) {
        try {
            List<Float> result = generateEmbedding(text);
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            log.error("Async embedding generation failed for text: {}", e.getMessage());
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 异步生成多个文本的embeddings
     */
    @Async("embeddingExecutor")
    public CompletableFuture<List<List<Float>>> generateEmbeddingsAsync(List<String> texts) {
        try {
            List<List<Float>> result = generateEmbeddings(texts);
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            log.error("Async batch embedding generation failed: {}", e.getMessage());
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 并行处理大批量文本（推荐用于大量文档处理）
     */
    @Async("embeddingExecutor")
    public CompletableFuture<List<List<Float>>> generateEmbeddingsParallel(List<String> texts) {
        if (texts == null || texts.isEmpty()) {
            return CompletableFuture.completedFuture(new ArrayList<>());
        }

        log.info("Starting parallel embedding generation for {} texts", texts.size());
        long startTime = System.currentTimeMillis();

        try {
            // 确定并行度
            int parallelism = Math.min(4, Math.max(1, texts.size() / 10));
            int chunkSize = Math.max(1, texts.size() / parallelism);

            List<CompletableFuture<List<List<Float>>>> futures = new ArrayList<>();

            // 分块并行处理
            for (int i = 0; i < texts.size(); i += chunkSize) {
                int endIndex = Math.min(i + chunkSize, texts.size());
                List<String> chunk = texts.subList(i, endIndex);

                CompletableFuture<List<List<Float>>> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        return generateEmbeddings(chunk);
                    } catch (Exception e) {
                        log.error("Parallel chunk processing failed: {}", e.getMessage());
                        throw new RuntimeException(e);
                    }
                });

                futures.add(future);
            }

            // 等待所有任务完成并合并结果
            CompletableFuture<List<List<Float>>> result = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
            ).thenApply(v -> {
                List<List<Float>> allResults = new ArrayList<>();
                for (CompletableFuture<List<List<Float>>> future : futures) {
                    try {
                        allResults.addAll(future.get());
                    } catch (Exception e) {
                        log.error("Failed to get parallel result: {}", e.getMessage());
                        throw new RuntimeException(e);
                    }
                }

                long endTime = System.currentTimeMillis();
                log.info("✅ Parallel embedding generation completed: {} texts in {}ms",
                    allResults.size(), endTime - startTime);

                return allResults;
            });

            return result;

        } catch (Exception e) {
            log.error("Parallel embedding generation failed: {}", e.getMessage());
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 获取缓存统计信息
     */
    public EmbeddingCacheService.CacheStats getCacheStats() {
        return cacheService.getCacheStats();
    }

    /**
     * 清空embedding缓存
     */
    public void clearCache() {
        cacheService.clearCache();
        log.info("Embedding cache cleared by user request");
    }
}
