package com.example.aicousin.service;

import com.example.aicousin.config.MilvusProperties;
import com.example.aicousin.entity.DocumentChunk;
import com.example.aicousin.entity.DatabaseSchemaChunk;
import io.milvus.client.MilvusServiceClient;
import io.milvus.grpc.DataType;
import io.milvus.grpc.GetCollectionStatisticsResponse;
import io.milvus.grpc.SearchResults;
import io.milvus.grpc.GetLoadStateResponse;
import io.milvus.grpc.LoadState; 
import io.milvus.param.IndexType;
import io.milvus.param.MetricType;
import io.milvus.param.R;
import io.milvus.param.RpcStatus;
import io.milvus.param.collection.*;
import io.milvus.param.dml.InsertParam;
import io.milvus.param.dml.SearchParam;
import io.milvus.param.dml.DeleteParam;
import io.milvus.param.index.CreateIndexParam;
import io.milvus.response.SearchResultsWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.time.LocalDateTime;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MilvusVectorService {

    private final MilvusServiceClient milvusClient;
    private final MilvusProperties milvusProperties;

    // Fields for document collection
    private static final String ID_FIELD = "id";
    private static final String DOCUMENT_ID_FIELD = "document_id";
    private static final String DOCUMENT_NAME_FIELD = "document_name";
    private static final String CONTENT_FIELD = "content";
    private static final String EMBEDDING_FIELD = "embedding";
    private static final String CHUNK_INDEX_FIELD = "chunk_index";
    private static final String CREATED_AT_FIELD = "created_at";
    private static final String METADATA_FIELD = "metadata"; 

    // Fields for schema collection
    private static final String DB_NAME_FIELD = "db_name";
    private static final String TABLE_NAME_FIELD = "table_name";
    private static final String SCHEMA_TEXT_FIELD = "schema_text";

    @PostConstruct
    public void initializeCollections() {
        try {
            // Initialize document collection
            log.info("Initializing document collection...");
            createCollectionIfNotExists(milvusProperties.getDocumentCollection());
            createIndexIfNotExists(milvusProperties.getDocumentCollection());
            loadCollection(milvusProperties.getDocumentCollection().getName());

            // Initialize schema collection
            log.info("Initializing schema collection...");
            createCollectionIfNotExists(milvusProperties.getSchemaCollection());
            createIndexIfNotExists(milvusProperties.getSchemaCollection());
            loadCollection(milvusProperties.getSchemaCollection().getName());

        } catch (Exception e) {
            log.error("Failed to initialize Milvus collections", e);
            throw new RuntimeException("Milvus initialization failed", e);
        }
    }

    private void createCollectionIfNotExists(MilvusProperties.Collection collectionConfig) {
        String collectionName = collectionConfig.getName();

        R<Boolean> hasCollectionResponse = milvusClient.hasCollection(
                HasCollectionParam.newBuilder()
                        .withCollectionName(collectionName)
                        .build()
        );

        if (hasCollectionResponse.getData()) {
            log.info("Collection {} already exists", collectionName);
            return;
        }

        log.info("Creating collection: {}", collectionName);

        List<FieldType> fields = new ArrayList<>();

        // Common fields
        fields.add(FieldType.newBuilder()
                .withName(ID_FIELD)
                .withDataType(DataType.VarChar)
                .withMaxLength(255)
                .withPrimaryKey(true)
                .withAutoID(false)
                .build());

        fields.add(FieldType.newBuilder()
                .withName(EMBEDDING_FIELD)
                .withDataType(DataType.FloatVector)
                .withDimension(collectionConfig.getDimension())
                .build());

        fields.add(FieldType.newBuilder()
                .withName(CREATED_AT_FIELD)
                .withDataType(DataType.VarChar)
                .withMaxLength(50)
                .build());

        // Specific fields based on collection type
        if (collectionName.equals(milvusProperties.getDocumentCollection().getName())) {
            fields.add(FieldType.newBuilder()
                    .withName(DOCUMENT_ID_FIELD)
                    .withDataType(DataType.VarChar)
                    .withMaxLength(255)
                    .build());
            fields.add(FieldType.newBuilder()
                    .withName(DOCUMENT_NAME_FIELD)
                    .withDataType(DataType.VarChar)
                    .withMaxLength(500)
                    .build());
            fields.add(FieldType.newBuilder()
                    .withName(CONTENT_FIELD)
                    .withDataType(DataType.VarChar)
                    .withMaxLength(65535)
                    .build());
            fields.add(FieldType.newBuilder()
                    .withName(CHUNK_INDEX_FIELD)
                    .withDataType(DataType.Int32)
                    .build());
            fields.add(FieldType.newBuilder()
                    .withName(METADATA_FIELD) 
                    .withDataType(DataType.VarChar)
                    .withMaxLength(65535)
                    .build());
        } else if (collectionName.equals(milvusProperties.getSchemaCollection().getName())) {
            fields.add(FieldType.newBuilder()
                    .withName(DB_NAME_FIELD)
                    .withDataType(DataType.VarChar)
                    .withMaxLength(255)
                    .build());
            fields.add(FieldType.newBuilder()
                    .withName(TABLE_NAME_FIELD)
                    .withDataType(DataType.VarChar)
                    .withMaxLength(500)
                    .build());
            fields.add(FieldType.newBuilder()
                    .withName(SCHEMA_TEXT_FIELD)
                    .withDataType(DataType.VarChar)
                    .withMaxLength(65535)
                    .build());
        }

        CreateCollectionParam createCollectionParam = CreateCollectionParam.newBuilder()
                .withCollectionName(collectionName)
                .withDescription(collectionName.equals(milvusProperties.getDocumentCollection().getName()) ?
                        "Document chunks with embeddings for RAG" : "Database schema information with embeddings for RAG")
                .withFieldTypes(fields)
                .build();

        R<RpcStatus> response = milvusClient.createCollection(createCollectionParam);
        if (response.getStatus() != R.Status.Success.getCode()) {
            throw new RuntimeException("Failed to create collection " + collectionName + ": " + response.getMessage());
        }

        log.info("Successfully created collection: {}", collectionName);
    }

    private void createIndexIfNotExists(MilvusProperties.Collection collectionConfig) {
        String collectionName = collectionConfig.getName();
        
        CreateIndexParam indexParam = CreateIndexParam.newBuilder()
                .withCollectionName(collectionName)
                .withFieldName(EMBEDDING_FIELD)
                .withIndexType(IndexType.valueOf(collectionConfig.getIndexType()))
                .withMetricType(MetricType.valueOf(collectionConfig.getMetricType()))
                .withExtraParam("{\"nlist\":" + collectionConfig.getNlist() + "}")
                .build();

        R<RpcStatus> response = milvusClient.createIndex(indexParam);
        if (response.getStatus() != R.Status.Success.getCode()) {
            log.warn("Index creation failed or already exists for collection {}: {}", collectionName, response.getMessage());
        } else {
            log.info("Created index for collection: {}", collectionName);
        }
    }

    private void loadCollection(String collectionName) {
        R<RpcStatus> response = milvusClient.loadCollection(
                LoadCollectionParam.newBuilder()
                        .withCollectionName(collectionName)
                        .build()
        );
        
        if (response.getStatus() != R.Status.Success.getCode()) {
            throw new RuntimeException("Failed to load collection " + collectionName + ": " + response.getMessage());
        }
        
        log.info("Loaded collection: {}", collectionName);
    }

    public void insertDocumentChunks(List<DocumentChunk> chunks) {
        if (chunks.isEmpty()) {
            return;
        }

        String collectionName = milvusProperties.getDocumentCollection().getName();
        log.info("Inserting {} document chunks into collection: {}", chunks.size(), collectionName);

        List<InsertParam.Field> fields = new ArrayList<>();
        fields.add(new InsertParam.Field(ID_FIELD, chunks.stream().map(DocumentChunk::getId).collect(Collectors.toList())));
        fields.add(new InsertParam.Field(DOCUMENT_ID_FIELD, chunks.stream().map(DocumentChunk::getDocumentId).collect(Collectors.toList())));
        fields.add(new InsertParam.Field(DOCUMENT_NAME_FIELD, chunks.stream().map(DocumentChunk::getDocumentName).collect(Collectors.toList())));
        fields.add(new InsertParam.Field(CONTENT_FIELD, chunks.stream().map(DocumentChunk::getContent).collect(Collectors.toList())));
        fields.add(new InsertParam.Field(EMBEDDING_FIELD, chunks.stream().map(DocumentChunk::getEmbedding).collect(Collectors.toList())));
        fields.add(new InsertParam.Field(CHUNK_INDEX_FIELD, chunks.stream().map(DocumentChunk::getChunkIndex).collect(Collectors.toList())));
        fields.add(new InsertParam.Field(CREATED_AT_FIELD, chunks.stream().map(c -> c.getCreatedAt().toString()).collect(Collectors.toList())));
        fields.add(new InsertParam.Field(METADATA_FIELD, chunks.stream().map(c -> c.getMetadata() != null ? c.getMetadata() : "").collect(Collectors.toList())));

        InsertParam insertParam = InsertParam.newBuilder()
                .withCollectionName(collectionName)
                .withFields(fields)
                .build();

        log.info("🚀 Inserting {} document chunks into Milvus...", chunks.size());
        R<io.milvus.grpc.MutationResult> response = milvusClient.insert(insertParam);
        if (response.getStatus() != R.Status.Success.getCode()) {
            log.error("❌ Failed to insert document chunks: {}", response.getMessage());
            throw new RuntimeException("Failed to insert document chunks: " + response.getMessage());
        }
        log.info("✅ Successfully inserted {} document chunks", chunks.size());
    }

    /**
     * 获取集合的可用输出字段（简化版本）
     */
    private List<String> getAvailableOutputFields(String collectionName) {
        // 先尝试包含所有字段，如果搜索失败再降级
        List<String> outputFields = new ArrayList<>();
        outputFields.add(ID_FIELD);
        outputFields.add(DOCUMENT_ID_FIELD);
        outputFields.add(DOCUMENT_NAME_FIELD);
        outputFields.add(CONTENT_FIELD);
        outputFields.add(CHUNK_INDEX_FIELD);
        outputFields.add(CREATED_AT_FIELD);

        // 暂时不包含METADATA_FIELD，避免字段不存在的错误
        log.info("Using basic output fields (excluding metadata to avoid field errors)");

        return outputFields;
    }

    /**
     * 获取基础输出字段（不包含可能不存在的字段）
     */
    private List<String> getBasicOutputFields() {
        return Arrays.asList(
            ID_FIELD, DOCUMENT_ID_FIELD, DOCUMENT_NAME_FIELD,
            CONTENT_FIELD, CHUNK_INDEX_FIELD, CREATED_AT_FIELD
        );
    }

    public void insertSchemaChunk(String dbName, String tableName, String schemaText, List<Float> embedding) {
        String collectionName = milvusProperties.getSchemaCollection().getName();
        log.info("Inserting schema chunk for {}.{} into collection: {}", dbName, tableName, collectionName);

        List<InsertParam.Field> fields = new ArrayList<>();
        fields.add(new InsertParam.Field(ID_FIELD, Collections.singletonList(UUID.randomUUID().toString())));
        fields.add(new InsertParam.Field(DB_NAME_FIELD, Collections.singletonList(dbName)));
        fields.add(new InsertParam.Field(TABLE_NAME_FIELD, Collections.singletonList(tableName)));
        fields.add(new InsertParam.Field(SCHEMA_TEXT_FIELD, Collections.singletonList(schemaText)));
        fields.add(new InsertParam.Field(EMBEDDING_FIELD, Collections.singletonList(embedding)));
        fields.add(new InsertParam.Field(CREATED_AT_FIELD, Collections.singletonList(LocalDateTime.now().toString())));

        InsertParam insertParam = InsertParam.newBuilder()
                .withCollectionName(collectionName)
                .withFields(fields)
                .build();

        log.info("🚀 Inserting schema chunk for {}.{} into Milvus...", dbName, tableName);
        R<io.milvus.grpc.MutationResult> response = milvusClient.insert(insertParam);
        if (response.getStatus() != R.Status.Success.getCode()) {
            log.error("❌ Failed to insert schema chunk for {}.{}: {}", dbName, tableName, response.getMessage());
            throw new RuntimeException("Failed to insert schema chunk: " + response.getMessage());
        }
        log.info("✅ Successfully inserted schema chunk for {}.{}", dbName, tableName);
    }

    public List<DocumentChunk> searchSimilarChunks(List<Float> queryEmbedding, int topK, String documentId) {
        String collectionName = milvusProperties.getDocumentCollection().getName();
        log.info("🔍 Searching for {} similar document chunks in collection: {}", topK, collectionName);
        log.info("📊 Query embedding size: {}", queryEmbedding.size());

        // 检查集合是否包含metadata字段
        List<String> outputFields = getAvailableOutputFields(collectionName);

        SearchParam.Builder searchParamBuilder = SearchParam.newBuilder()
                .withCollectionName(collectionName)
                .withMetricType(MetricType.valueOf(milvusProperties.getDocumentCollection().getMetricType()))
                .withOutFields(outputFields)
                .withTopK(topK)
                .withVectorFieldName(EMBEDDING_FIELD)
                .withVectors(Collections.singletonList(queryEmbedding));

        if (documentId != null && !documentId.isEmpty()) {
            searchParamBuilder.withExpr(String.format("%s == \"%s\"", DOCUMENT_ID_FIELD, documentId));
            log.info("Filtering document search by documentId: {}", documentId);
        }

        R<SearchResults> response = milvusClient.search(searchParamBuilder.build());

        if (response.getStatus() != R.Status.Success.getCode()) {
            log.error("❌ Milvus document search failed: {}", response.getMessage());
            return Collections.emptyList();
        }

        SearchResultsWrapper wrapper = new SearchResultsWrapper(response.getData().getResults());
        List<DocumentChunk> resultChunks = new ArrayList<>();

        // 安全地从SearchResultsWrapper中提取数据
        for (int i = 0; i < wrapper.getFieldData(ID_FIELD, 0).size(); i++) {
            String id = (String) wrapper.getFieldData(ID_FIELD, 0).get(i);
            String docId = (String) wrapper.getFieldData(DOCUMENT_ID_FIELD, 0).get(i);
            String docName = (String) wrapper.getFieldData(DOCUMENT_NAME_FIELD, 0).get(i);
            String content = (String) wrapper.getFieldData(CONTENT_FIELD, 0).get(i);
            Integer chunkIndex = (Integer) wrapper.getFieldData(CHUNK_INDEX_FIELD, 0).get(i);
            String createdAtStr = (String) wrapper.getFieldData(CREATED_AT_FIELD, 0).get(i);

            // 安全地获取metadata字段（如果存在）
            String metadata = "";
            try {
                if (outputFields.contains(METADATA_FIELD)) {
                    metadata = (String) wrapper.getFieldData(METADATA_FIELD, 0).get(i);
                }
            } catch (Exception e) {
                log.debug("Metadata field not available or empty for chunk {}", id);
                metadata = "";
            }

            // 创建一个空的embedding列表（搜索时不需要返回embedding）
            List<Float> embedding = new ArrayList<>();

            resultChunks.add(new DocumentChunk(
                    id,
                    docId,
                    docName,
                    content,
                    embedding,
                    chunkIndex,
                    LocalDateTime.parse(createdAtStr),
                    metadata
            ));
        }
        log.info("✅ Retrieved {} similar document chunks.", resultChunks.size());
        return resultChunks;
    }

    public List<DatabaseSchemaChunk> searchSimilarSchemaChunks(List<Float> queryEmbedding, int topK, String dbName, String tableName) {
        String collectionName = milvusProperties.getSchemaCollection().getName();
        log.info("🔍 Searching for {} similar schema chunks in collection: {}", topK, collectionName);

        List<String> outputFields = Arrays.asList(
                ID_FIELD, DB_NAME_FIELD, TABLE_NAME_FIELD, SCHEMA_TEXT_FIELD, CREATED_AT_FIELD, EMBEDDING_FIELD
        );

        SearchParam.Builder searchParamBuilder = SearchParam.newBuilder()
                .withCollectionName(collectionName)
                .withMetricType(MetricType.valueOf(milvusProperties.getSchemaCollection().getMetricType()))
                .withOutFields(outputFields)
                .withTopK(topK)
                .withVectorFieldName(EMBEDDING_FIELD)
                .withVectors(Collections.singletonList(queryEmbedding));

        // Build expression for filtering
        StringBuilder expr = new StringBuilder();
        if (dbName != null && !dbName.isEmpty()) {
            expr.append(String.format("%s == \"%s\"", DB_NAME_FIELD, dbName));
        }
        if (tableName != null && !tableName.isEmpty()) {
            if (expr.length() > 0) {
                expr.append(" and ");
            }
            expr.append(String.format("%s == \"%s\"", TABLE_NAME_FIELD, tableName));
        }
        if (expr.length() > 0) {
            searchParamBuilder.withExpr(expr.toString());
            log.info("Filtering schema search by expression: {}", expr.toString());
        }

        R<SearchResults> response = milvusClient.search(searchParamBuilder.build());

        if (response.getStatus() != R.Status.Success.getCode()) {
            log.error("❌ Milvus schema search failed: {}", response.getMessage());
            return Collections.emptyList();
        }

        SearchResultsWrapper wrapper = new SearchResultsWrapper(response.getData().getResults());
        List<DatabaseSchemaChunk> resultChunks = new ArrayList<>();

        for (int i = 0; i < wrapper.getFieldData(ID_FIELD, 0).size(); i++) {
            String id = (String) wrapper.getFieldData(ID_FIELD, 0).get(i);
            String retrievedDbName = (String) wrapper.getFieldData(DB_NAME_FIELD, 0).get(i);
            String retrievedTableName = (String) wrapper.getFieldData(TABLE_NAME_FIELD, 0).get(i);
            String schemaText = (String) wrapper.getFieldData(SCHEMA_TEXT_FIELD, 0).get(i);
            @SuppressWarnings("unchecked")
            List<Float> retrievedEmbedding = (List<Float>) wrapper.getFieldData(EMBEDDING_FIELD, 0).get(i);
            String createdAtStr = (String) wrapper.getFieldData(CREATED_AT_FIELD, 0).get(i);

            resultChunks.add(new DatabaseSchemaChunk(
                    id,
                    retrievedDbName,
                    retrievedTableName,
                    schemaText,
                    retrievedEmbedding,
                    LocalDateTime.parse(createdAtStr)
            ));
        }
        log.info("✅ Retrieved {} similar schema chunks.", resultChunks.size());
        return resultChunks;
    }

    public void deleteDocumentChunks(String documentId) {
        String collectionName = milvusProperties.getDocumentCollection().getName();
        log.info("Deleting chunks for document ID: {} from collection: {}", documentId, collectionName);

        String expr = String.format("%s == \"%s\"", DOCUMENT_ID_FIELD, documentId);
        DeleteParam deleteParam = DeleteParam.newBuilder()
                .withCollectionName(collectionName)
                .withExpr(expr)
                .build();

        R<io.milvus.grpc.MutationResult> response = milvusClient.delete(deleteParam);
        if (response.getStatus() != R.Status.Success.getCode()) {
            log.error("❌ Failed to delete document chunks for ID {}: {}", documentId, response.getMessage());
            throw new RuntimeException("Failed to delete document chunks: " + response.getMessage());
        }
        log.info("✅ Successfully deleted chunks for document ID: {}", documentId);
    }

    public void deleteSchemaChunks(String dbName, String tableName) {
        String collectionName = milvusProperties.getSchemaCollection().getName();
        log.info("Deleting schema chunks for database {} and table {} from collection: {}", dbName, tableName, collectionName);

        StringBuilder expr = new StringBuilder();
        if (dbName != null && !dbName.isEmpty()) {
            expr.append(String.format("%s == \"%s\"", DB_NAME_FIELD, dbName));
        }
        if (tableName != null && !tableName.isEmpty()) {
            if (expr.length() > 0) {
                expr.append(" and ");
            }
            expr.append(String.format("%s == \"%s\"", TABLE_NAME_FIELD, tableName));
        }

        if (expr.length() == 0) {
            log.warn("Attempted to delete schema chunks without specifying dbName or tableName. Aborting.");
            return; // Prevent accidental mass deletion
        }

        DeleteParam deleteParam = DeleteParam.newBuilder()
                .withCollectionName(collectionName)
                .withExpr(expr.toString())
                .build();

        R<io.milvus.grpc.MutationResult> response = milvusClient.delete(deleteParam);
        if (response.getStatus() != R.Status.Success.getCode()) {
            log.error("❌ Failed to delete schema chunks for {}.{}: {}", dbName, tableName, response.getMessage());
            throw new RuntimeException("Failed to delete schema chunks: " + response.getMessage());
        }
        log.info("✅ Successfully deleted schema chunks for {}.{}", dbName, tableName);
    }

    public Map<String, Object> getCollectionStats() {
        Map<String, Object> stats = new HashMap<>();

        // Document Collection Stats
        String docCollectionName = milvusProperties.getDocumentCollection().getName();
        R<GetCollectionStatisticsResponse> docStatsResponse = milvusClient.getCollectionStatistics(
                GetCollectionStatisticsParam.newBuilder().withCollectionName(docCollectionName).build()
        );
        if (docStatsResponse.getStatus() == R.Status.Success.getCode()) {
            // Milvus GetCollectionStatisticsResponse provides stats as a list of KeyValuePair
            // Find the "row_count" key
            long documentCount = docStatsResponse.getData().getStatsList().stream()
                    .filter(s -> "row_count".equals(s.getKey()))
                    .mapToLong(s -> Long.parseLong(s.getValue()))
                    .findFirst().orElse(0L);

            stats.put("documentCollection", Map.of(
                    "name", docCollectionName,
                    "entityCount", documentCount
            ));
        } else {
            stats.put("documentCollection", Map.of("name", docCollectionName, "error", docStatsResponse.getMessage()));
        }

        // Schema Collection Stats
        String schemaCollectionName = milvusProperties.getSchemaCollection().getName();
        R<GetCollectionStatisticsResponse> schemaStatsResponse = milvusClient.getCollectionStatistics(
                GetCollectionStatisticsParam.newBuilder().withCollectionName(schemaCollectionName).build()
        );
        if (schemaStatsResponse.getStatus() == R.Status.Success.getCode()) {
            long schemaCount = schemaStatsResponse.getData().getStatsList().stream()
                    .filter(s -> "row_count".equals(s.getKey()))
                    .mapToLong(s -> Long.parseLong(s.getValue()))
                    .findFirst().orElse(0L);
            stats.put("schemaCollection", Map.of(
                    "name", schemaCollectionName,
                    "entityCount", schemaCount
            ));
        } else {
            stats.put("schemaCollection", Map.of("name", schemaCollectionName, "error", schemaStatsResponse.getMessage()));
        }

        return stats;
    }

    public boolean isCollectionLoaded(String collectionName) {
        // Reverting to GetLoadingProgressParam based on analysis of original code and repeated errors
        R<io.milvus.grpc.GetLoadingProgressResponse> response = milvusClient.getLoadingProgress(
                io.milvus.param.collection.GetLoadingProgressParam.newBuilder()
                        .withCollectionName(collectionName)
                        .build()
        );
        if (response.getStatus() == R.Status.Success.getCode()) {
            return response.getData().getProgress() == 100;
        } else {
            log.warn("Failed to get loading progress for collection {}: {}", collectionName, response.getMessage());
            return false;
        }
    }

    /**
     * 重建文档集合（如果字段结构不匹配）
     */
    public void rebuildDocumentCollection() {
        String collectionName = milvusProperties.getDocumentCollection().getName();
        log.warn("🔄 Rebuilding document collection: {}", collectionName);

        try {
            // 删除现有集合
            R<RpcStatus> dropResponse = milvusClient.dropCollection(
                DropCollectionParam.newBuilder()
                    .withCollectionName(collectionName)
                    .build()
            );

            if (dropResponse.getStatus() == R.Status.Success.getCode()) {
                log.info("✅ Dropped existing collection: {}", collectionName);
            } else {
                log.warn("⚠️ Failed to drop collection or collection doesn't exist: {}", dropResponse.getMessage());
            }

            // 重新创建集合
            createCollectionIfNotExists(milvusProperties.getDocumentCollection());
            createIndexIfNotExists(milvusProperties.getDocumentCollection());
            loadCollection(collectionName);

            log.info("✅ Successfully rebuilt collection: {}", collectionName);

        } catch (Exception e) {
            log.error("❌ Failed to rebuild collection: {}", e.getMessage());
            throw new RuntimeException("Failed to rebuild collection: " + e.getMessage());
        }
    }

    /**
     * 检查并修复集合结构（简化版本）
     */
    public boolean checkAndFixCollectionStructure() {
        String collectionName = milvusProperties.getDocumentCollection().getName();

        try {
            log.info("🔧 Attempting to fix Milvus collection structure for: {}", collectionName);

            // 简单的修复策略：重建集合
            log.warn("🔄 Rebuilding collection to fix field structure issues...");
            rebuildDocumentCollection();
            return true;

        } catch (Exception e) {
            log.error("❌ Error fixing collection structure: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取所有文档的元数据信息（用于恢复文档列表）
     */
    public List<Map<String, Object>> getAllDocumentMetadata() {
        String collectionName = milvusProperties.getDocumentCollection().getName();
        log.info("🔍 Recovering document metadata from Milvus collection: {}", collectionName);

        try {
            // 查询所有文档的基本信息，按document_id分组
            List<String> outputFields = Arrays.asList(
                DOCUMENT_ID_FIELD, DOCUMENT_NAME_FIELD, CREATED_AT_FIELD
            );

            // 使用空的向量进行查询（获取所有数据）
            List<Float> dummyVector = new ArrayList<>();
            for (int i = 0; i < milvusProperties.getDocumentCollection().getDimension(); i++) {
                dummyVector.add(0.0f);
            }

            SearchParam searchParam = SearchParam.newBuilder()
                .withCollectionName(collectionName)
                .withVectorFieldName(EMBEDDING_FIELD)
                .withVectors(Collections.singletonList(dummyVector))
                .withTopK(10000) // 获取大量结果
                .withMetricType(MetricType.valueOf(milvusProperties.getDocumentCollection().getMetricType()))
                .withOutFields(outputFields)
                .withParams("{\"nprobe\": 10}")
                .build();

            R<SearchResults> searchResponse = milvusClient.search(searchParam);
            if (searchResponse.getStatus() != R.Status.Success.getCode()) {
                log.error("Failed to query document metadata: {}", searchResponse.getMessage());
                return new ArrayList<>();
            }

            SearchResultsWrapper wrapper = new SearchResultsWrapper(searchResponse.getData().getResults());
            Map<String, Map<String, Object>> documentMap = new HashMap<>();

            // 处理查询结果，按document_id去重
            for (int i = 0; i < wrapper.getFieldData(DOCUMENT_ID_FIELD, 0).size(); i++) {
                String documentId = (String) wrapper.getFieldData(DOCUMENT_ID_FIELD, 0).get(i);
                String documentName = (String) wrapper.getFieldData(DOCUMENT_NAME_FIELD, 0).get(i);
                String createdAt = (String) wrapper.getFieldData(CREATED_AT_FIELD, 0).get(i);

                if (!documentMap.containsKey(documentId)) {
                    Map<String, Object> docInfo = new HashMap<>();
                    docInfo.put("documentId", documentId);
                    docInfo.put("documentName", documentName);
                    docInfo.put("createdAt", createdAt);
                    docInfo.put("chunksCount", 1);
                    documentMap.put(documentId, docInfo);
                } else {
                    // 增加分块计数
                    Map<String, Object> existing = documentMap.get(documentId);
                    int count = (Integer) existing.get("chunksCount");
                    existing.put("chunksCount", count + 1);
                }
            }

            List<Map<String, Object>> result = new ArrayList<>(documentMap.values());
            log.info("✅ Recovered {} documents from Milvus", result.size());
            return result;

        } catch (Exception e) {
            log.error("❌ Failed to recover document metadata from Milvus: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 获取指定文档的分块数量
     */
    public int getDocumentChunksCount(String documentId) {
        String collectionName = milvusProperties.getDocumentCollection().getName();

        try {
            // 使用表达式查询特定文档的分块数量
            String expr = String.format("%s == \"%s\"", DOCUMENT_ID_FIELD, documentId);

            // 创建一个简单的查询来计算分块数量
            List<Float> dummyVector = new ArrayList<>();
            for (int i = 0; i < milvusProperties.getDocumentCollection().getDimension(); i++) {
                dummyVector.add(0.0f);
            }

            SearchParam searchParam = SearchParam.newBuilder()
                .withCollectionName(collectionName)
                .withVectorFieldName(EMBEDDING_FIELD)
                .withVectors(Collections.singletonList(dummyVector))
                .withTopK(10000)
                .withExpr(expr)
                .withMetricType(MetricType.valueOf(milvusProperties.getDocumentCollection().getMetricType()))
                .withOutFields(Collections.singletonList(ID_FIELD))
                .withParams("{\"nprobe\": 10}")
                .build();

            R<SearchResults> searchResponse = milvusClient.search(searchParam);
            if (searchResponse.getStatus() != R.Status.Success.getCode()) {
                log.warn("Failed to count chunks for document {}: {}", documentId, searchResponse.getMessage());
                return 0;
            }

            SearchResultsWrapper wrapper = new SearchResultsWrapper(searchResponse.getData().getResults());
            int count = wrapper.getFieldData(ID_FIELD, 0).size();

            log.debug("Document {} has {} chunks", documentId, count);
            return count;

        } catch (Exception e) {
            log.error("Failed to count chunks for document {}: {}", documentId, e.getMessage());
            return 0;
        }
    }
}
