package com.example.aicousin.service;

import com.example.aicousin.dto.DatabaseConnectionRequest;
import com.example.aicousin.dto.TableSchemaInfo;
import com.example.aicousin.dto.TableSchemaInfo.ColumnInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.HashSet;

@Slf4j
@Service
@RequiredArgsConstructor
public class DatabaseSchemaService {

    private final EmbeddingService embeddingService;
    private final MilvusVectorService milvusVectorService;

    /**
     * Test database connection and return available tables.
     * @param request Database connection details.
     * @return List of table names.
     * @throws SQLException if connection fails or metadata cannot be retrieved.
     */
    public List<String> getAvailableTables(DatabaseConnectionRequest request) throws SQLException {
        String jdbcUrl = buildJdbcUrl(request);
        log.info("Attempting to connect to: {}", jdbcUrl);

        try (Connection conn = DriverManager.getConnection(jdbcUrl, request.getUsername(), request.getPassword())) {
            DatabaseMetaData metaData = conn.getMetaData();
            List<String> tables = new ArrayList<>();
            // For most databases, "TABLE" type is sufficient. Some might use "BASE TABLE"
            try (ResultSet rs = metaData.getTables(request.getDatabaseName(), null, "%", new String[]{"TABLE"})) {
                while (rs.next()) {
                    tables.add(rs.getString("TABLE_NAME"));
                }
            }
            log.info("Successfully retrieved {} tables from database: {}", tables.size(), request.getDatabaseName());
            return tables;
        } catch (SQLException e) {
            log.error("Failed to connect to database or retrieve tables: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Get schema information for a specific table and vectorize it.
     * @param request Database connection details.
     * @param tableName The name of the table to get schema from.
     * @return TableSchemaInfo containing formatted schema text and details.
     * @throws SQLException if schema cannot be retrieved or connection fails.
     */
    public TableSchemaInfo getAndVectorizeTableSchema(DatabaseConnectionRequest request, String tableName) throws SQLException {
        String jdbcUrl = buildJdbcUrl(request);
        log.info("Retrieving schema for table: {} from {}", tableName, jdbcUrl);

        try (Connection conn = DriverManager.getConnection(jdbcUrl, request.getUsername(), request.getPassword())) {
            TableSchemaInfo schemaInfo = new TableSchemaInfo();
            schemaInfo.setTableName(tableName);
            List<ColumnInfo> columns = new ArrayList<>();
            Set<String> primaryKeys = new HashSet<>();

            // Get primary keys
            try (ResultSet pkRs = conn.getMetaData().getPrimaryKeys(request.getDatabaseName(), null, tableName)) {
                while (pkRs.next()) {
                    primaryKeys.add(pkRs.getString("COLUMN_NAME"));
                }
            }

            // Get columns and their properties
            try (ResultSet rs = conn.getMetaData().getColumns(request.getDatabaseName(), null, tableName, "%" )) {
                while (rs.next()) {
                    ColumnInfo column = new ColumnInfo();
                    column.setColumnName(rs.getString("COLUMN_NAME"));
                    column.setColumnType(rs.getString("TYPE_NAME"));
                    column.setNullable(rs.getInt("NULLABLE") == DatabaseMetaData.columnNullable);
                    // Try to get column comment/remarks, varies by DB
                    try {
                        column.setComment(rs.getString("REMARKS"));
                    } catch (SQLException e) {
                        log.warn("REMARKS column not found for {}.{}.{}", request.getDatabaseName(), tableName, column.getColumnName());
                        column.setComment(null);
                    }
                    column.setPrimaryKey(primaryKeys.contains(column.getColumnName()));
                    columns.add(column);
                }
            }
            schemaInfo.setColumns(columns);
            schemaInfo.setSchemaText(formatSchemaText(request.getDatabaseName(), tableName, columns));

            // Vectorize the schema text
            List<Float> embedding = embeddingService.generateEmbedding(schemaInfo.getSchemaText());

            // Store in Milvus
            milvusVectorService.insertSchemaChunk(
                    request.getDatabaseName(),
                    tableName,
                    schemaInfo.getSchemaText(),
                    embedding
            );

            log.info("Successfully processed and vectorized schema for table: {}.{}", request.getDatabaseName(), tableName);
            return schemaInfo;
        } catch (SQLException e) {
            log.error("Failed to retrieve or vectorize schema for table {}: {}", tableName, e.getMessage());
            throw e;
        }
    }

    /**
     * Builds JDBC URL based on database type.
     */
    private String buildJdbcUrl(DatabaseConnectionRequest request) {
        switch (request.getDbType().toLowerCase()) {
            case "mysql":
                return String.format("***************************************************************",
                        request.getHost(), request.getPort(), request.getDatabaseName());
            case "postgresql":
                return String.format("jdbc:postgresql://%s:%d/%s",
                        request.getHost(), request.getPort(), request.getDatabaseName());
            case "sqlserver": // Microsoft SQL Server
                return String.format("**************************************",
                        request.getHost(), request.getPort(), request.getDatabaseName());
            case "oracle":
                return String.format("**************************",
                        request.getHost(), request.getPort(), request.getDatabaseName());
            default:
                throw new IllegalArgumentException("Unsupported database type: " + request.getDbType());
        }
    }

    /**
     * Formats the table schema into a human-readable text for embedding.
     */
    private String formatSchemaText(String databaseName, String tableName, List<ColumnInfo> columns) {
        StringBuilder sb = new StringBuilder();
        sb.append(String.format("Database: %s\n", databaseName));
        sb.append(String.format("Table: %s\n", tableName));
        sb.append("Columns:\n");
        for (ColumnInfo col : columns) {
            sb.append(String.format("- %s (Type: %s%s%s)%s\n",
                    col.getColumnName(),
                    col.getColumnType(),
                    col.isPrimaryKey() ? ", Primary Key" : "",
                    col.isNullable() ? ", Nullable" : ", Not Null",
                    StringUtils.hasText(col.getComment()) ? ", Comment: " + col.getComment() : ""
            ));
        }
        return sb.toString();
    }
} 