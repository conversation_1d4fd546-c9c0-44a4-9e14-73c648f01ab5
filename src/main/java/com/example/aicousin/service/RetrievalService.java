package com.example.aicousin.service;

import com.example.aicousin.config.RagProperties;
import com.example.aicousin.dto.QueryResponse;
import com.example.aicousin.entity.DocumentChunk;
import com.example.aicousin.entity.DatabaseSchemaChunk;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class RetrievalService {

    private final EmbeddingService embeddingService;
    private final MilvusVectorService milvusVectorService;
    private final RagProperties ragProperties;

    /**
     * Defines the source types for retrieval.
     */
    public enum SearchSourceType {
        DOCUMENTS, // Search only document chunks
        DATABASE_SCHEMAS, // Search only database schema chunks
        ALL // Search both document and database schema chunks
    }

    /**
     * Retrieve relevant chunks (documents or database schemas) for a query.
     * @param query The user's query string.
     * @param topK The maximum number of top-K results to retrieve.
     * @param similarityThreshold The minimum similarity score for a chunk to be considered relevant.
     * @param documentId Optional: filter document chunks by a specific document ID.
     * @param dbName Optional: filter schema chunks by a specific database name.
     * @param tableName Optional: filter schema chunks by a specific table name.
     * @param sourceType The type of source to search (DOCUMENTS, DATABASE_SCHEMAS, ALL).
     * @return List of retrieved chunks.
     */
    public List<QueryResponse.RetrievedChunk> retrieveRelevantChunks(
            String query, 
            Integer topK, 
            Double similarityThreshold,
            String documentId,
            String dbName,
            String tableName,
            SearchSourceType sourceType) {
        
        log.debug("Retrieving chunks for query: {}", query);
        
        // Generate embedding for query
        List<Float> queryEmbedding = embeddingService.generateEmbedding(query);
        
        // Use provided parameters or defaults
        int actualTopK = topK != null ? topK : ragProperties.getRetrieval().getTopK();
        double actualThreshold = similarityThreshold != null ? 
                similarityThreshold : ragProperties.getRetrieval().getSimilarityThreshold();
        
        List<QueryResponse.RetrievedChunk> allChunks = new ArrayList<>();

        if (sourceType == SearchSourceType.DOCUMENTS || sourceType == SearchSourceType.ALL) {
            // Search for similar document chunks
            List<DocumentChunk> similarDocumentChunks = milvusVectorService.searchSimilarChunks(
                    queryEmbedding, actualTopK, documentId);
            
            // Convert to response format
            allChunks.addAll(similarDocumentChunks.stream()
                    .map(chunk -> {
                        double similarity = calculateCosineSimilarity(queryEmbedding, chunk.getEmbedding());
                        return QueryResponse.RetrievedChunk.builder()
                                .chunkId(chunk.getId())
                                .documentName(chunk.getDocumentName())
                                .content(chunk.getContent())
                                .similarity(similarity)
                                .chunkIndex(chunk.getChunkIndex())
                                .sourceType(SearchSourceType.DOCUMENTS.name().toLowerCase())
                                .build();
                    })
                    .collect(Collectors.toList()));
        }

        if (sourceType == SearchSourceType.DATABASE_SCHEMAS || sourceType == SearchSourceType.ALL) {
            // Search for similar database schema chunks
            List<DatabaseSchemaChunk> similarSchemaChunks = milvusVectorService.searchSimilarSchemaChunks(
                    queryEmbedding, actualTopK, dbName, tableName);

            // Convert to response format
            allChunks.addAll(similarSchemaChunks.stream()
                    .map(chunk -> {
                        double similarity = calculateCosineSimilarity(queryEmbedding, chunk.getEmbedding());
                        return QueryResponse.RetrievedChunk.builder()
                                .chunkId(chunk.getId())
                                .dbName(chunk.getDbName())
                                .tableName(chunk.getTableName())
                                .content(chunk.getSchemaText()) // Use schemaText as content
                                .similarity(similarity)
                                .sourceType(SearchSourceType.DATABASE_SCHEMAS.name().toLowerCase())
                                .build();
                    })
                    .collect(Collectors.toList()));
        }

        // Sort all chunks by similarity in descending order and limit to topK
        List<QueryResponse.RetrievedChunk> sortedAndLimitedChunks = allChunks.stream()
                .sorted(Comparator.comparingDouble(QueryResponse.RetrievedChunk::getSimilarity).reversed())
                .limit(actualTopK)
                .collect(Collectors.toList());

        // Log similarity distribution (from sorted and limited list)
        log.info("📊 检索到 {} 个片段 ({}个文档, {}个Schema)，相似度分布:", 
            sortedAndLimitedChunks.size(),
            sortedAndLimitedChunks.stream().filter(c -> SearchSourceType.DOCUMENTS.name().toLowerCase().equals(c.getSourceType())).count(),
            sortedAndLimitedChunks.stream().filter(c -> SearchSourceType.DATABASE_SCHEMAS.name().toLowerCase().equals(c.getSourceType())).count()
        );

        sortedAndLimitedChunks.forEach(chunk ->
            log.info("  - [{}]{}{}{} 相似度: {:.4f}",
                chunk.getSourceType().toUpperCase(),
                chunk.getDocumentName() != null ? " (" + chunk.getDocumentName() + ")" : "",
                chunk.getDbName() != null ? " (" + chunk.getDbName() + "." + chunk.getTableName() + ")" : "",
                chunk.getChunkIndex() != 0 ? " (块" + chunk.getChunkIndex() + ")" : "",
                chunk.getSimilarity()));

        // Apply similarity threshold filtering
        List<QueryResponse.RetrievedChunk> filteredChunks = sortedAndLimitedChunks.stream()
                .filter(chunk -> chunk.getSimilarity() >= actualThreshold)
                .collect(Collectors.toList());

        log.info("🎯 阈值过滤后剩余 {} 个片段 (阈值: {:.3f})", filteredChunks.size(), actualThreshold);

        if (filteredChunks.isEmpty() && !sortedAndLimitedChunks.isEmpty()) {
            log.warn("⚠️ 所有检索结果都被相似度阈值过滤掉了！");
            log.warn("💡 建议降低相似度阈值或检查embedding质量");

            // If threshold is 0 or less, return all sorted results (already limited by topK)
            if (actualThreshold <= 0.0) {
                log.info("🔄 阈值为0，返回所有检索结果");
                filteredChunks = sortedAndLimitedChunks; // Already sorted and limited
            }
            // If using fallback embedding, automatically lower threshold
            else if (isUsingFallbackEmbedding()) {
                double fallbackThreshold = Math.max(actualThreshold * 0.5, 0.1); // Minimum 0.1
                log.info("🔄 检测到使用fallback embedding，自动降低阈值到: {:.3f}", fallbackThreshold);

                filteredChunks = sortedAndLimitedChunks.stream()
                        .filter(chunk -> chunk.getSimilarity() >= fallbackThreshold)
                        .collect(Collectors.toList());

                log.info("✅ 降低阈值后获得 {} 个结果", filteredChunks.size());

                // If still no results, return the top 3 most similar from the sorted list
                if (filteredChunks.isEmpty()) {
                    log.info("🔄 仍无结果，返回相似度最高的3个结果");
                    filteredChunks = sortedAndLimitedChunks.stream()
                            .sorted(Comparator.comparingDouble(QueryResponse.RetrievedChunk::getSimilarity).reversed())
                            .limit(3)
                            .collect(Collectors.toList());
                }
            }
        }

        return filteredChunks;
    }

    /**
     * Calculate cosine similarity between two vectors
     */
    private double calculateCosineSimilarity(List<Float> vector1, List<Float> vector2) {
        if (vector1 == null || vector2 == null || vector1.size() != vector2.size()) {
            return 0.0;
        }

        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (int i = 0; i < vector1.size(); i++) {
            dotProduct += vector1.get(i) * vector2.get(i);
            norm1 += Math.pow(vector1.get(i), 2);
            norm2 += Math.pow(vector2.get(i), 2);
        }

        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    /**
     * 检测是否正在使用fallback embedding
     */
    private boolean isUsingFallbackEmbedding() {
        return !embeddingService.isEmbeddingModelAvailable();
    }
}
