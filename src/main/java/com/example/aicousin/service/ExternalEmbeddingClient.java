package com.example.aicousin.service;

import com.example.aicousin.config.ExternalEmbeddingProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 外部向量化服务客户端
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExternalEmbeddingClient {
    
    private final ExternalEmbeddingProperties properties;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    /**
     * 请求DTO
     */
    @Data
    public static class EmbeddingRequest {
        private List<String> texts;
        
        public EmbeddingRequest(List<String> texts) {
            this.texts = texts;
        }
    }
    
    /**
     * 响应DTO
     */
    @Data
    public static class EmbeddingResponse {
        private String model;
        private List<List<Float>> data;
        private Integer dims;
        
        @JsonProperty("model")
        public String getModel() {
            return model;
        }
        
        @JsonProperty("data")
        public List<List<Float>> getData() {
            return data;
        }
        
        @JsonProperty("dims")
        public Integer getDims() {
            return dims;
        }
    }
    
    /**
     * 测试外部向量化服务是否可用
     */
    public boolean testConnection() {
        if (!properties.isEnabled()) {
            log.debug("External embedding service is disabled");
            return false;
        }
        
        try {
            log.info("🔍 Testing external embedding service at: {}", properties.getFullUrl());
            
            List<String> testTexts = List.of("test");
            EmbeddingResponse response = callEmbeddingAPI(testTexts);
            
            if (response != null && response.getData() != null && !response.getData().isEmpty()) {
                log.info("✅ External embedding service is available");
                log.info("   Model: {}", response.getModel());
                log.info("   Dimensions: {}", response.getDims());
                return true;
            } else {
                log.warn("⚠️ External embedding service returned empty response");
                return false;
            }
            
        } catch (Exception e) {
            log.warn("❌ External embedding service not available: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 生成单个文本的向量
     */
    public List<Float> generateEmbedding(String text) {
        List<List<Float>> embeddings = generateEmbeddings(List.of(text));
        return embeddings.isEmpty() ? null : embeddings.get(0);
    }
    
    /**
     * 生成多个文本的向量
     */
    public List<List<Float>> generateEmbeddings(List<String> texts) {
        if (!properties.isEnabled()) {
            throw new IllegalStateException("External embedding service is not enabled");
        }
        
        if (texts == null || texts.isEmpty()) {
            throw new IllegalArgumentException("Texts cannot be null or empty");
        }
        
        try {
            log.debug("Generating embeddings for {} texts using external service", texts.size());
            
            EmbeddingResponse response = callEmbeddingAPI(texts);
            
            if (response == null || response.getData() == null) {
                throw new RuntimeException("External embedding service returned null response");
            }
            
            if (response.getData().size() != texts.size()) {
                throw new RuntimeException(String.format(
                    "Mismatch in response size: expected %d, got %d", 
                    texts.size(), response.getData().size()
                ));
            }
            
            log.debug("Successfully generated {} embeddings", response.getData().size());
            return response.getData();
            
        } catch (Exception e) {
            log.error("Failed to generate embeddings using external service: {}", e.getMessage());
            throw new RuntimeException("External embedding generation failed", e);
        }
    }
    
    /**
     * 调用外部向量化API
     */
    private EmbeddingResponse callEmbeddingAPI(List<String> texts) {
        int retries = 0;
        Exception lastException = null;
        
        while (retries <= properties.getMaxRetries()) {
            try {
                // 创建请求
                EmbeddingRequest request = new EmbeddingRequest(texts);
                
                // 设置请求头
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                
                // 如果配置了API密钥，添加认证头
                if (properties.getApiKey() != null && !properties.getApiKey().isEmpty()) {
                    headers.setBearerAuth(properties.getApiKey());
                }
                
                HttpEntity<EmbeddingRequest> entity = new HttpEntity<>(request, headers);
                
                // 发送请求
                long startTime = System.currentTimeMillis();
                ResponseEntity<EmbeddingResponse> response = restTemplate.exchange(
                    properties.getFullUrl(),
                    HttpMethod.POST,
                    entity,
                    EmbeddingResponse.class
                );
                long endTime = System.currentTimeMillis();
                
                log.debug("External embedding API call took {}ms", endTime - startTime);
                
                if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                    return response.getBody();
                } else {
                    throw new RuntimeException("API returned non-success status: " + response.getStatusCode());
                }
                
            } catch (Exception e) {
                lastException = e;
                retries++;
                
                if (retries <= properties.getMaxRetries()) {
                    long delay = Math.min(1000L * retries, 5000L); // 指数退避，最大5秒
                    log.warn("External embedding API call failed (attempt {}/{}), retrying in {}ms: {}", 
                        retries, properties.getMaxRetries() + 1, delay, e.getMessage());
                    
                    try {
                        TimeUnit.MILLISECONDS.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Interrupted during retry delay", ie);
                    }
                } else {
                    log.error("External embedding API call failed after {} attempts", retries);
                }
            }
        }
        
        throw new RuntimeException("External embedding API call failed after all retries", lastException);
    }
    
    /**
     * 获取服务信息
     */
    public String getServiceInfo() {
        if (!properties.isEnabled()) {
            return "External embedding service is disabled";
        }
        
        return String.format("External Embedding Service: %s (enabled: %s)", 
            properties.getFullUrl(), properties.isEnabled());
    }
}
