package com.example.aicousin.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryResponse {

    private String answer;
    private String query;
    private List<RetrievedChunk> retrievedChunks;
    private long processingTimeMs;
    private String embeddingStatus; // 新增：embedding状态信息
    private boolean usingFallbackEmbedding; // 新增：是否使用fallback

    // 手动添加getter/setter方法
    public String getAnswer() { return answer; }
    public void setAnswer(String answer) { this.answer = answer; }

    public String getQuery() { return query; }
    public void setQuery(String query) { this.query = query; }

    public List<RetrievedChunk> getRetrievedChunks() { return retrievedChunks; }
    public void setRetrievedChunks(List<RetrievedChunk> retrievedChunks) { this.retrievedChunks = retrievedChunks; }

    public long getProcessingTimeMs() { return processingTimeMs; }
    public void setProcessingTimeMs(long processingTimeMs) { this.processingTimeMs = processingTimeMs; }

    public String getEmbeddingStatus() { return embeddingStatus; }
    public void setEmbeddingStatus(String embeddingStatus) { this.embeddingStatus = embeddingStatus; }

    public boolean isUsingFallbackEmbedding() { return usingFallbackEmbedding; }
    public void setUsingFallbackEmbedding(boolean usingFallbackEmbedding) { this.usingFallbackEmbedding = usingFallbackEmbedding; }

    // 手动添加builder方法
    public static QueryResponseBuilder builder() {
        return new QueryResponseBuilder();
    }

    public static class QueryResponseBuilder {
        private String answer;
        private String query;
        private List<RetrievedChunk> retrievedChunks;
        private long processingTimeMs;
        private String embeddingStatus;
        private boolean usingFallbackEmbedding;

        public QueryResponseBuilder answer(String answer) {
            this.answer = answer;
            return this;
        }

        public QueryResponseBuilder query(String query) {
            this.query = query;
            return this;
        }

        public QueryResponseBuilder retrievedChunks(List<RetrievedChunk> retrievedChunks) {
            this.retrievedChunks = retrievedChunks;
            return this;
        }

        public QueryResponseBuilder processingTimeMs(long processingTimeMs) {
            this.processingTimeMs = processingTimeMs;
            return this;
        }

        public QueryResponseBuilder embeddingStatus(String embeddingStatus) {
            this.embeddingStatus = embeddingStatus;
            return this;
        }

        public QueryResponseBuilder usingFallbackEmbedding(boolean usingFallbackEmbedding) {
            this.usingFallbackEmbedding = usingFallbackEmbedding;
            return this;
        }

        public QueryResponse build() {
            QueryResponse response = new QueryResponse();
            response.answer = this.answer;
            response.query = this.query;
            response.retrievedChunks = this.retrievedChunks;
            response.processingTimeMs = this.processingTimeMs;
            response.embeddingStatus = this.embeddingStatus;
            response.usingFallbackEmbedding = this.usingFallbackEmbedding;
            return response;
        }
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RetrievedChunk {
        private String chunkId;
        private String documentName;
        private String content;
        private double similarity;
        private int chunkIndex;
        private String sourceType; // New: "document" or "db_schema"
        private String dbName;     // New: Database name for schema chunks
        private String tableName;  // New: Table name for schema chunks

        // 手动添加getter/setter方法
        public String getChunkId() { return chunkId; }
        public void setChunkId(String chunkId) { this.chunkId = chunkId; }

        public String getDocumentName() { return documentName; }
        public void setDocumentName(String documentName) { this.documentName = documentName; }

        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }

        public double getSimilarity() { return similarity; }
        public void setSimilarity(double similarity) { this.similarity = similarity; }

        public int getChunkIndex() { return chunkIndex; }
        public void setChunkIndex(int chunkIndex) { this.chunkIndex = chunkIndex; }

        // 手动添加builder方法
        public static RetrievedChunkBuilder builder() {
            return new RetrievedChunkBuilder();
        }

        public static class RetrievedChunkBuilder {
            private String chunkId;
            private String documentName;
            private String content;
            private double similarity;
            private int chunkIndex;
            private String sourceType;
            private String dbName;
            private String tableName;

            public RetrievedChunkBuilder chunkId(String chunkId) {
                this.chunkId = chunkId;
                return this;
            }

            public RetrievedChunkBuilder documentName(String documentName) {
                this.documentName = documentName;
                return this;
            }

            public RetrievedChunkBuilder content(String content) {
                this.content = content;
                return this;
            }

            public RetrievedChunkBuilder similarity(double similarity) {
                this.similarity = similarity;
                return this;
            }

            public RetrievedChunkBuilder chunkIndex(int chunkIndex) {
                this.chunkIndex = chunkIndex;
                return this;
            }

            public RetrievedChunkBuilder sourceType(String sourceType) {
                this.sourceType = sourceType;
                return this;
            }

            public RetrievedChunkBuilder dbName(String dbName) {
                this.dbName = dbName;
                return this;
            }

            public RetrievedChunkBuilder tableName(String tableName) {
                this.tableName = tableName;
                return this;
            }

            public RetrievedChunk build() {
                RetrievedChunk chunk = new RetrievedChunk();
                chunk.chunkId = this.chunkId;
                chunk.documentName = this.documentName;
                chunk.content = this.content;
                chunk.similarity = this.similarity;
                chunk.chunkIndex = this.chunkIndex;
                chunk.sourceType = this.sourceType;
                chunk.dbName = this.dbName;
                chunk.tableName = this.tableName;
                return chunk;
            }
        }
    }
}
