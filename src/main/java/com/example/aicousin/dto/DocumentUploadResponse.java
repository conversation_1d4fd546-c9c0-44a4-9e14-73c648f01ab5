package com.example.aicousin.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentUploadResponse {

    private String documentId;
    private String documentName;
    private int chunksCreated;
    private long processingTimeMs;
    private String status;
    private String message;
    private String embeddingStatus; // 新增：embedding状态信息
    private boolean usingFallbackEmbedding; // 新增：是否使用fallback

    // 手动添加getter/setter方法
    public String getDocumentId() { return documentId; }
    public void setDocumentId(String documentId) { this.documentId = documentId; }

    public String getDocumentName() { return documentName; }
    public void setDocumentName(String documentName) { this.documentName = documentName; }

    public int getChunksCreated() { return chunksCreated; }
    public void setChunksCreated(int chunksCreated) { this.chunksCreated = chunksCreated; }

    public long getProcessingTimeMs() { return processingTimeMs; }
    public void setProcessingTimeMs(long processingTimeMs) { this.processingTimeMs = processingTimeMs; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }

    public String getEmbeddingStatus() { return embeddingStatus; }
    public void setEmbeddingStatus(String embeddingStatus) { this.embeddingStatus = embeddingStatus; }

    public boolean isUsingFallbackEmbedding() { return usingFallbackEmbedding; }
    public void setUsingFallbackEmbedding(boolean usingFallbackEmbedding) { this.usingFallbackEmbedding = usingFallbackEmbedding; }

    // 手动添加builder方法
    public static DocumentUploadResponseBuilder builder() {
        return new DocumentUploadResponseBuilder();
    }

    public static class DocumentUploadResponseBuilder {
        private String documentId;
        private String documentName;
        private int chunksCreated;
        private long processingTimeMs;
        private String status;
        private String message;
        private String embeddingStatus;
        private boolean usingFallbackEmbedding;

        public DocumentUploadResponseBuilder documentId(String documentId) {
            this.documentId = documentId;
            return this;
        }

        public DocumentUploadResponseBuilder documentName(String documentName) {
            this.documentName = documentName;
            return this;
        }

        public DocumentUploadResponseBuilder chunksCreated(int chunksCreated) {
            this.chunksCreated = chunksCreated;
            return this;
        }

        public DocumentUploadResponseBuilder processingTimeMs(long processingTimeMs) {
            this.processingTimeMs = processingTimeMs;
            return this;
        }

        public DocumentUploadResponseBuilder status(String status) {
            this.status = status;
            return this;
        }

        public DocumentUploadResponseBuilder message(String message) {
            this.message = message;
            return this;
        }

        public DocumentUploadResponseBuilder embeddingStatus(String embeddingStatus) {
            this.embeddingStatus = embeddingStatus;
            return this;
        }

        public DocumentUploadResponseBuilder usingFallbackEmbedding(boolean usingFallbackEmbedding) {
            this.usingFallbackEmbedding = usingFallbackEmbedding;
            return this;
        }

        public DocumentUploadResponse build() {
            DocumentUploadResponse response = new DocumentUploadResponse();
            response.documentId = this.documentId;
            response.documentName = this.documentName;
            response.chunksCreated = this.chunksCreated;
            response.processingTimeMs = this.processingTimeMs;
            response.status = this.status;
            response.message = this.message;
            response.embeddingStatus = this.embeddingStatus;
            response.usingFallbackEmbedding = this.usingFallbackEmbedding;
            return response;
        }
    }
}
