package com.example.aicousin.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentChunk {

    private String id;
    private String documentId;
    private String documentName;
    private String content;
    private List<Float> embedding;
    private int chunkIndex;
    private LocalDateTime createdAt;
    private String metadata;

    // 手动添加getter方法以确保编译通过
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDocumentId() {
        return documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getDocumentName() {
        return documentName;
    }

    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<Float> getEmbedding() {
        return embedding;
    }

    public void setEmbedding(List<Float> embedding) {
        this.embedding = embedding;
    }

    public int getChunkIndex() {
        return chunkIndex;
    }

    public void setChunkIndex(int chunkIndex) {
        this.chunkIndex = chunkIndex;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    // 手动添加builder方法
    public static DocumentChunkBuilder builder() {
        return new DocumentChunkBuilder();
    }

    public static class DocumentChunkBuilder {
        private String id;
        private String documentId;
        private String documentName;
        private String content;
        private List<Float> embedding;
        private int chunkIndex;
        private LocalDateTime createdAt;
        private String metadata;

        public DocumentChunkBuilder id(String id) {
            this.id = id;
            return this;
        }

        public DocumentChunkBuilder documentId(String documentId) {
            this.documentId = documentId;
            return this;
        }

        public DocumentChunkBuilder documentName(String documentName) {
            this.documentName = documentName;
            return this;
        }

        public DocumentChunkBuilder content(String content) {
            this.content = content;
            return this;
        }

        public DocumentChunkBuilder embedding(List<Float> embedding) {
            this.embedding = embedding;
            return this;
        }

        public DocumentChunkBuilder chunkIndex(int chunkIndex) {
            this.chunkIndex = chunkIndex;
            return this;
        }

        public DocumentChunkBuilder createdAt(LocalDateTime createdAt) {
            this.createdAt = createdAt;
            return this;
        }

        public DocumentChunkBuilder metadata(String metadata) {
            this.metadata = metadata;
            return this;
        }

        public DocumentChunk build() {
            DocumentChunk chunk = new DocumentChunk();
            chunk.id = this.id;
            chunk.documentId = this.documentId;
            chunk.documentName = this.documentName;
            chunk.content = this.content;
            chunk.embedding = this.embedding;
            chunk.chunkIndex = this.chunkIndex;
            chunk.createdAt = this.createdAt;
            chunk.metadata = this.metadata;
            return chunk;
        }
    }
}
