package com.example.aicousin.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DatabaseSchemaChunk {
    private String id;
    private String dbName;
    private String tableName;
    private String schemaText;
    private List<Float> embedding;
    private LocalDateTime createdAt;
} 