package com.example.aicousin;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.core.env.Environment;
import org.springframework.context.ConfigurableApplicationContext;

@Slf4j
@SpringBootApplication
public class AiCousinApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(AiCousinApplication.class, args);
        Environment env = context.getEnvironment();

        // 显示当前使用的配置文件信息
        String[] activeProfiles = env.getActiveProfiles();
        String[] defaultProfiles = env.getDefaultProfiles();

        log.info("🚀 AI Cousin RAG System Started Successfully!");
        log.info("📋 Active Profiles: {}", activeProfiles.length > 0 ? String.join(", ", activeProfiles) : "None");
        log.info("📋 Default Profiles: {}", String.join(", ", defaultProfiles));
        log.info("🌐 Server Port: {}", env.getProperty("server.port", "8080/index-new.html"));
        log.info("🗄️ Milvus Host: {}", env.getProperty("milvus.host", "Not configured"));
        log.info("🔑 DeepSeek API Key: {}", env.getProperty("spring.ai.openai.api-key", "").isEmpty() ? "Not configured" : "Configured");
        log.info("📖 Access Web UI at: http://localhost:{}", env.getProperty("server.port", "8080"));
    }
}
