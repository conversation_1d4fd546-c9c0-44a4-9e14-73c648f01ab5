#!/bin/bash

# AI Cousin Development Environment Startup Script

echo "🚀 Starting AI Cousin Development Environment..."
echo

# Check if required environment variables are set
if [ -z "$DEEPSEEK_API_KEY" ]; then
    echo "❌ Error: DEEPSEEK_API_KEY environment variable is not set"
    echo "Please set your DeepSeek API key:"
    echo "export DEEPSEEK_API_KEY=your-deepseek-api-key"
    exit 1
fi

echo "✅ DeepSeek API Key is configured"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Error: Docker is not running"
    echo "Please start Docker and try again"
    exit 1
fi

echo "✅ Docker is running"

# Check if Mi<PERSON><PERSON><PERSON> is already running
if docker ps | grep -q milvus-standalone; then
    echo "✅ Milvus is already running"
else
    echo "🔄 Starting Milvus..."
    docker run -d --name milvus-standalone \
        -p 19530:19530 \
        -p 9091:9091 \
        -v $(pwd)/volumes/milvus:/var/lib/milvus \
        milvusdb/milvus:v2.3.4 \
        milvus run standalone
    
    echo "⏳ Waiting for Milvu<PERSON> to start..."
    sleep 10
    
    # Wait for Mil<PERSON><PERSON> to be ready
    max_attempts=30
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:9091/healthz > /dev/null 2>&1; then
            echo "✅ Milvus is ready"
            break
        fi
        echo "⏳ Waiting for Milvus... (attempt $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        echo "❌ Error: Milvus failed to start within expected time"
        exit 1
    fi
fi

# Check if Java is installed
if ! command -v java &> /dev/null; then
    echo "❌ Error: Java is not installed"
    echo "Please install Java 17 or later"
    exit 1
fi

# Check Java version
java_version=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | cut -d'.' -f1)
if [ "$java_version" -lt 17 ]; then
    echo "❌ Error: Java 17 or later is required (found Java $java_version)"
    exit 1
fi

echo "✅ Java $java_version is installed"

# Check if Maven is installed
if ! command -v mvn &> /dev/null; then
    echo "❌ Error: Maven is not installed"
    echo "Please install Maven 3.6 or later"
    exit 1
fi

echo "✅ Maven is installed"

# Set environment variables
export MILVUS_HOST=localhost
export MILVUS_PORT=19530

echo "🔧 Environment variables set:"
echo "   MILVUS_HOST=$MILVUS_HOST"
echo "   MILVUS_PORT=$MILVUS_PORT"
echo "   DEEPSEEK_API_KEY=***"
echo "   DEEPSEEK_BASE_URL=${DEEPSEEK_BASE_URL:-https://api.deepseek.com}"

echo
echo "🏗️  Building application..."
mvn clean compile -q

if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to build application"
    exit 1
fi

echo "✅ Application built successfully"

echo
echo "🚀 Starting AI Cousin application..."
echo "📱 Web interface will be available at: http://localhost:8080"
echo "🔗 API endpoints will be available at: http://localhost:8080/api/rag"
echo
echo "Press Ctrl+C to stop the application"
echo

# Start the Spring Boot application
mvn spring-boot:run
