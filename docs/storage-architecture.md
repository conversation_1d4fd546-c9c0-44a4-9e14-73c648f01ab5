# AI Cousin 向量库存储架构

## 架构概览

AI Cousin 系统采用混合存储架构，将不同类型的数据存储在最适合的位置，同时提供数据恢复机制确保系统的可靠性。

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户上传文件   │    │   文档处理流程   │    │   存储分布架构   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ PDF/Word/Text   │───▶│ 文本提取+分块    │───▶│ 内存 + <PERSON><PERSON><PERSON><PERSON>   │
│ 原始文件        │    │ 向量化处理      │    │ 混合存储        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 存储层次结构

### 1. 临时存储层（处理阶段）

**用途**: 文件上传和处理过程中的临时存储

| 组件 | 存储内容 | 生命周期 | 位置 |
|------|----------|----------|------|
| MultipartFile | 原始上传文件 | 请求处理期间 | 内存/临时目录 |
| Document对象 | 提取的文本内容 | 处理期间 | 内存 |
| DocumentChunk | 分块后的文本 | 处理期间 | 内存 |

### 2. 元数据存储层（快速访问）

**用途**: 文档基本信息的快速查询和展示

```java
// 内存存储 - ConcurrentHashMap
private final Map<String, DocumentInfo> documents = new ConcurrentHashMap<>();

public class DocumentInfo {
    private String id;              // 文档唯一标识
    private String name;            // 文档名称
    private String originalName;    // 原始文件名
    private long size;              // 文件大小
    private LocalDateTime uploadTime; // 上传时间
    private int chunks;             // 分块数量
    private String status;          // 处理状态
    private String filePath;        // 文件路径（未使用）
    private String contentType;     // 内容类型
}
```

**特点**:
- ✅ 快速访问（O(1)查询）
- ✅ 支持并发操作
- ❌ 重启后数据丢失
- ❌ 无法跨实例共享

### 3. 向量存储层（持久化）

**用途**: 文档内容的向量化存储和相似性搜索

```sql
-- Milvus Collection Schema
CREATE COLLECTION ai_cousin_documents (
    id VARCHAR(255) PRIMARY KEY,
    document_id VARCHAR(255),
    document_name VARCHAR(255),
    content TEXT,
    embedding FLOAT_VECTOR(1536),
    chunk_index INT,
    created_at VARCHAR(255),
    metadata TEXT
);
```

**特点**:
- ✅ 持久化存储
- ✅ 高性能向量搜索
- ✅ 分布式架构
- ✅ 数据备份和恢复
- ❌ 查询延迟相对较高

## 数据流转过程

### 上传和处理流程

```
1. 文件上传
   ├── MultipartFile (临时)
   └── 文件验证

2. 文档处理
   ├── 文本提取 → Document对象 (内存)
   ├── 内容分块 → DocumentChunk列表 (内存)
   └── 向量化处理 → 添加embedding

3. 数据存储
   ├── 元数据 → DocumentService.documents (内存)
   └── 向量数据 → MilvusVectorService (持久化)

4. 清理
   └── 临时对象被GC回收
```

### 查询和检索流程

```
1. 文档列表查询
   └── DocumentService.getAllDocuments() (内存)

2. RAG问答查询
   ├── 问题向量化
   ├── Milvus相似性搜索 (持久化)
   ├── 结果排序和过滤
   └── LLM生成回答

3. 文档删除
   ├── 从内存删除元数据
   └── 从Milvus删除向量数据
```

## 恢复机制设计

### 问题分析

**核心问题**: 内存存储的元数据在服务重启后丢失，导致：
- 前端文档列表为空
- 无法显示文档统计信息
- 用户体验下降

**根本原因**: 元数据和向量数据存储分离，缺乏同步机制

### 解决方案

#### 1. 自动恢复机制

```java
@PostConstruct
@DependsOn("milvusVectorService")
public void recoverDocumentsFromMilvus() {
    // 应用启动时自动执行
    // 从Milvus查询所有文档元数据
    // 重建内存中的文档映射
}
```

**优势**:
- 无需人工干预
- 确保数据一致性
- 快速恢复服务

#### 2. 手动恢复API

```http
POST /api/rag/documents/recover
```

**优势**:
- 支持运行时恢复
- 提供恢复统计信息
- 便于故障排查

#### 3. 智能恢复逻辑

```java
// 检查集合状态
if (entityCount == 0) {
    return new ArrayList<>(); // 空集合，无需恢复
}

// 查询并去重
Map<String, DocumentInfo> documentMap = new HashMap<>();
// 按document_id分组，统计分块数量
```

**优势**:
- 避免无效查询
- 数据去重和聚合
- 错误处理和容错

## 架构优势

### 1. 性能优化

| 操作类型 | 存储层 | 性能特点 |
|----------|--------|----------|
| 文档列表查询 | 内存 | 毫秒级响应 |
| 相似性搜索 | Milvus | 秒级响应，支持大规模数据 |
| 文档上传 | 混合 | 并行处理，异步存储 |

### 2. 可靠性保障

- **数据持久化**: 核心向量数据永久保存
- **自动恢复**: 服务重启后自动恢复元数据
- **手动恢复**: 支持故障时手动干预
- **错误处理**: 完善的异常处理机制

### 3. 扩展性支持

- **水平扩展**: Milvus支持分布式部署
- **存储扩展**: 可轻松增加存储容量
- **功能扩展**: 支持多种文档类型和向量模型

## 未来优化方向

### 1. 数据库持久化

```sql
-- 建议的数据库表结构
CREATE TABLE documents (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255),
    size BIGINT,
    upload_time TIMESTAMP,
    chunks_count INT,
    status VARCHAR(50),
    content_type VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**优势**:
- 完整的元数据持久化
- 支持复杂查询和统计
- 事务支持和数据一致性

### 2. 缓存层优化

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Redis    │    │   Database  │    │   Milvus    │
│   (缓存层)   │    │  (元数据)   │    │  (向量层)   │
└─────────────┘    └─────────────┘    └─────────────┘
```

**优势**:
- 提高查询性能
- 支持分布式缓存
- 减少数据库压力

### 3. 同步机制

- **定时同步**: 定期同步内存和持久化数据
- **事件驱动**: 基于消息队列的数据同步
- **版本控制**: 数据版本管理和冲突解决

## 监控和运维

### 关键指标

- 文档总数和存储大小
- 恢复成功率和耗时
- Milvus连接状态和性能
- 内存使用情况

### 告警机制

- 恢复失败告警
- 数据不一致告警
- 存储容量告警
- 性能异常告警

这个架构设计在保证性能的同时，提供了可靠的数据恢复机制，为AI Cousin系统的稳定运行提供了坚实的基础。
