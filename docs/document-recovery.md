# 文档恢复功能说明

## 概述

本系统实现了从Milvus向量数据库恢复文档元数据的功能，解决了服务重启后文档列表丢失的问题。

## 存储架构

### 当前存储方式：混合模式

| 数据类型 | 存储位置 | 持久性 | 说明 |
|---------|---------|--------|------|
| 文档元数据 | 内存 (ConcurrentHashMap) | 临时性 | 文档基本信息，重启后丢失 |
| 向量数据 | Milvus远程数据库 | 永久性 | 文档分块内容 + 向量embeddings |
| 原始文件 | 不保存 | 临时性 | 处理完成后丢弃 |

### 问题与解决方案

**问题**: 服务重启后，内存中的文档元数据丢失，导致前端文档列表为空，但Milvus中的向量数据仍然存在。

**解决方案**: 实现从Milvus恢复文档元数据的机制。

## 功能实现

### 1. 自动恢复机制

应用启动时自动执行恢复：

```java
@PostConstruct
public void recoverDocumentsFromMilvus() {
    // 从Milvus查询所有文档元数据
    // 重建内存中的文档映射
}
```

**特点**:
- 应用启动时自动执行
- 使用 `@DependsOn("milvusVectorService")` 确保正确的初始化顺序
- 如果内存中已有文档，则跳过恢复

### 2. 手动恢复API

提供REST API端点进行手动恢复：

```http
POST /api/rag/documents/recover
```

**响应示例**:
```json
{
  "status": "success",
  "message": "文档元数据恢复完成",
  "beforeCount": 0,
  "afterCount": 5,
  "recoveredCount": 5,
  "timestamp": 1690876543210
}
```

### 3. 恢复逻辑

1. **检查集合状态**: 首先检查Milvus集合是否为空
2. **查询文档数据**: 使用向量搜索获取所有文档分块
3. **数据去重**: 按document_id分组，统计每个文档的分块数量
4. **重建元数据**: 创建DocumentInfo对象并添加到内存映射

## 使用方法

### 自动恢复

无需手动操作，应用启动时自动执行：

```bash
# 启动应用
mvn spring-boot:run
```

日志输出：
```
🔄 正在从Milvus恢复文档元数据...
✅ 成功从Milvus恢复了 5 个文档的元数据
```

### 手动恢复

使用curl命令：

```bash
curl -X POST http://localhost:8080/api/rag/documents/recover
```

使用前端界面：
- 在管理页面点击"恢复文档"按钮
- 系统会显示恢复结果

### 测试验证

运行测试类验证功能：

```bash
mvn test -Dtest=DocumentRecoveryTest
```

## 技术细节

### 核心类和方法

1. **DocumentService**
   - `recoverDocumentsFromMilvus()`: 主恢复方法
   - `clearDocuments()`: 清空内存文档（测试用）

2. **MilvusVectorService**
   - `getAllDocumentMetadata()`: 从Milvus查询文档元数据
   - `getCollectionStats()`: 获取集合统计信息

3. **RagController**
   - `recoverDocumentsFromMilvus()`: 手动恢复API端点

### 数据流程

```
Milvus向量数据库
    ↓ (查询所有分块)
文档元数据提取
    ↓ (按document_id分组)
DocumentInfo对象创建
    ↓ (添加到内存映射)
文档列表恢复完成
```

### 错误处理

- **空集合**: 如果Milvus集合为空，直接返回，不执行查询
- **查询失败**: 捕获异常，记录错误日志，不影响应用启动
- **数据异常**: 安全地处理缺失字段，使用默认值

## 限制和注意事项

### 恢复的数据

**可以恢复**:
- 文档ID和名称
- 分块数量
- 创建时间
- 处理状态

**无法恢复**:
- 原始文件大小（设为0）
- 文件路径（设为null）
- 内容类型（设为null）

### 性能考虑

- 大量文档时恢复可能较慢
- 建议在低峰期进行手动恢复
- 自动恢复会延长应用启动时间

### 数据一致性

- 恢复的数据基于Milvus中的实际向量数据
- 确保数据的准确性和一致性
- 不会产生重复或冲突的文档记录

## 监控和日志

### 关键日志

```
🔄 正在从Milvus恢复文档元数据...
ℹ️ Collection ai_cousin_documents is empty, no documents to recover
✅ 成功从Milvus恢复了 X 个文档的元数据
❌ 从Milvus恢复文档元数据失败: [错误信息]
```

### 监控指标

- 恢复的文档数量
- 恢复耗时
- 成功/失败率
- Milvus连接状态

## 未来改进

1. **数据库持久化**: 将文档元数据存储到关系数据库
2. **增量恢复**: 只恢复新增或变更的文档
3. **定时同步**: 定期同步Milvus和内存数据
4. **缓存优化**: 使用Redis等缓存提高性能
