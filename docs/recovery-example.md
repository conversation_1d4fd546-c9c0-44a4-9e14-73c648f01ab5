# 文档恢复功能使用示例

## 场景演示

假设您已经上传了一些文档到系统中，然后重启了服务。

### 步骤1: 上传文档（模拟已有数据）

```bash
# 上传一个测试文档
curl -X POST http://localhost:8080/api/rag/documents \
  -F "file=@test-document.pdf"
```

响应：
```json
{
  "documentId": "doc_1690876543_abc12345",
  "documentName": "test-document.pdf",
  "chunksCreated": 15,
  "status": "SUCCESS",
  "message": "Document processed and stored successfully"
}
```

### 步骤2: 查看文档列表

```bash
curl http://localhost:8080/api/rag/documents
```

响应：
```json
{
  "documents": [
    {
      "id": "doc_1690876543_abc12345",
      "name": "test-document.pdf",
      "originalName": "test-document.pdf",
      "size": "2.5 MB",
      "uploadTime": "2025-07-31 17:00:00",
      "chunks": 15,
      "status": "已处理",
      "contentType": "application/pdf"
    }
  ],
  "total": 1,
  "timestamp": 1690876543210
}
```

### 步骤3: 重启服务（模拟问题）

```bash
# 停止服务
pkill -f "ai-cousin"

# 重新启动服务
mvn spring-boot:run
```

### 步骤4: 查看重启后的文档列表

```bash
curl http://localhost:8080/api/rag/documents
```

**没有恢复功能时的响应**：
```json
{
  "documents": [],
  "total": 0,
  "timestamp": 1690876600000
}
```

**有恢复功能时的响应**：
```json
{
  "documents": [
    {
      "id": "doc_1690876543_abc12345",
      "name": "test-document.pdf",
      "originalName": "test-document.pdf",
      "size": "0 B",  // 注意：文件大小无法恢复
      "uploadTime": "2025-07-31 17:00:00",
      "chunks": 15,
      "status": "已处理",
      "contentType": null  // 注意：内容类型无法恢复
    }
  ],
  "total": 1,
  "timestamp": 1690876600000
}
```

### 步骤5: 手动触发恢复（可选）

如果自动恢复失败或需要重新恢复：

```bash
curl -X POST http://localhost:8080/api/rag/documents/recover
```

响应：
```json
{
  "status": "success",
  "message": "文档元数据恢复完成",
  "beforeCount": 0,
  "afterCount": 1,
  "recoveredCount": 1,
  "timestamp": 1690876700000
}
```

## 验证恢复效果

### 1. 检查RAG功能

恢复后，RAG问答功能应该正常工作：

```bash
curl -X POST http://localhost:8080/api/rag/query \
  -H "Content-Type: application/json" \
  -d '{
    "question": "文档中提到了什么内容？",
    "similarityThreshold": 0.7
  }'
```

### 2. 检查Milvus统计

```bash
curl http://localhost:8080/api/rag/documents
```

查看响应中的 `milvusStats` 字段：
```json
{
  "milvusStats": {
    "documentCollection": {
      "name": "ai_cousin_documents",
      "entityCount": 15
    },
    "schemaCollection": {
      "name": "ai_cousin_schema", 
      "entityCount": 0
    }
  }
}
```

## 测试脚本

创建一个完整的测试脚本：

```bash
#!/bin/bash

echo "=== 文档恢复功能测试 ==="

# 1. 上传测试文档
echo "1. 上传测试文档..."
curl -X POST http://localhost:8080/api/rag/documents \
  -F "file=@test.txt" \
  -o upload_response.json

echo "上传响应："
cat upload_response.json
echo ""

# 2. 查看文档列表
echo "2. 查看上传后的文档列表..."
curl http://localhost:8080/api/rag/documents \
  -o documents_before.json

echo "上传后文档数量："
cat documents_before.json | jq '.total'

# 3. 手动清空文档（模拟重启）
echo "3. 清空内存中的文档（模拟重启）..."
# 这里需要调用清空API或重启服务

# 4. 查看清空后的文档列表
echo "4. 查看清空后的文档列表..."
curl http://localhost:8080/api/rag/documents \
  -o documents_after_clear.json

echo "清空后文档数量："
cat documents_after_clear.json | jq '.total'

# 5. 手动触发恢复
echo "5. 手动触发文档恢复..."
curl -X POST http://localhost:8080/api/rag/documents/recover \
  -o recovery_response.json

echo "恢复响应："
cat recovery_response.json
echo ""

# 6. 查看恢复后的文档列表
echo "6. 查看恢复后的文档列表..."
curl http://localhost:8080/api/rag/documents \
  -o documents_after_recovery.json

echo "恢复后文档数量："
cat documents_after_recovery.json | jq '.total'

# 7. 测试RAG功能
echo "7. 测试RAG问答功能..."
curl -X POST http://localhost:8080/api/rag/query \
  -H "Content-Type: application/json" \
  -d '{"question": "测试问题", "similarityThreshold": 0.7}' \
  -o rag_response.json

echo "RAG响应："
cat rag_response.json | jq '.answer'

echo "=== 测试完成 ==="
```

## 常见问题

### Q1: 恢复后文档信息不完整？

**A**: 这是正常的，因为某些信息（如文件大小、内容类型）只存在于内存中，无法从Milvus恢复。

### Q2: 恢复失败怎么办？

**A**: 检查以下几点：
1. Milvus连接是否正常
2. 集合中是否有数据
3. 查看错误日志获取详细信息

### Q3: 可以恢复删除的文档吗？

**A**: 如果文档的向量数据还在Milvus中，就可以恢复元数据。但如果已经从Milvus中删除，则无法恢复。

### Q4: 恢复会影响性能吗？

**A**: 恢复过程会查询Milvus，如果文档数量很大，可能会影响启动时间。建议在低峰期进行。

## 最佳实践

1. **定期备份**: 考虑将文档元数据持久化到数据库
2. **监控恢复**: 监控恢复成功率和耗时
3. **测试验证**: 定期测试恢复功能的有效性
4. **文档管理**: 及时清理不需要的文档，减少恢复负担
