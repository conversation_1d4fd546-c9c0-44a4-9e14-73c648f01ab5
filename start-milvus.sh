#!/bin/bash

echo "🚀 Starting Milvus for AI Cousin..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Error: Dock<PERSON> is not running"
    echo "Please start Docker Desktop and try again"
    exit 1
fi

# Check if <PERSON><PERSON><PERSON><PERSON> is already running
if docker ps | grep -q milvus-standalone; then
    echo "✅ Milvus is already running"
    echo "📊 Milvus status:"
    docker ps | grep milvus-standalone
else
    echo "🔄 Starting Milvus standalone..."
    
    # Create volumes directory if it doesn't exist
    mkdir -p volumes/milvus
    
    # Start Milvus
    docker run -d --name milvus-standalone \
        -p 19530:19530 \
        -p 9091:9091 \
        -v $(pwd)/volumes/milvus:/var/lib/milvus \
        milvusdb/milvus:v2.3.4 \
        milvus run standalone
    
    echo "⏳ Waiting for Milvus to start..."
    sleep 10
    
    # Check if Mil<PERSON><PERSON> is healthy
    max_attempts=30
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:9091/healthz > /dev/null 2>&1; then
            echo "✅ Milvus is ready!"
            break
        fi
        echo "⏳ Waiting for Milvus... (attempt $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        echo "❌ Milvus failed to start within expected time"
        echo "📋 Check logs with: docker logs milvus-standalone"
        exit 1
    fi
fi

echo ""
echo "🎉 Milvus is ready!"
echo "📍 Milvus endpoint: localhost:19530"
echo "🔍 Health check: http://localhost:9091/healthz"
echo ""
echo "Now you can run AI Cousin in IDEA:"
echo "1. Set DEEPSEEK_API_KEY in your environment variables"
echo "2. Run AiCousinApplication with 'local' profile"
echo "3. Access the web interface at http://localhost:8080"
